#ifndef __ADC_H
#define __ADC_H

#include "sys.h"

// ADC通道定义
#define ADC_CHANNEL_COUNT       4
#define ADC_CHANNEL_0           ADC_CHANNEL_0    // PA0
#define ADC_CHANNEL_1           ADC_CHANNEL_1    // PA1
#define ADC_CHANNEL_2           ADC_CHANNEL_2    // PA2
#define ADC_CHANNEL_3           ADC_CHANNEL_3    // PA3

// ADC参数定义
#define ADC_RESOLUTION          4096    // 12位ADC分辨率
#define ADC_VREF                3.3f    // 参考电压(V)
#define ADC_SAMPLE_COUNT        10      // 采样次数(用于滤波)

// ADC状态
typedef enum {
    ADC_OK = 0,
    ADC_ERROR,
    ADC_BUSY,
    ADC_TIMEOUT
} ADCStatus_t;

// ADC数据结构
typedef struct {
    u16 raw_value[ADC_CHANNEL_COUNT];       // 原始ADC值
    float voltage[ADC_CHANNEL_COUNT];       // 电压值(V)
    u32 sample_buffer[ADC_CHANNEL_COUNT][ADC_SAMPLE_COUNT]; // 采样缓冲区
    u8 sample_index;                        // 采样索引
    ADCStatus_t status;                     // ADC状态
    u32 last_update_time;                   // 上次更新时间
} ADCData_t;

// 函数声明
void ADC_Init(void);
ADCStatus_t ADC_ReadChannel(u8 channel, u16* value);
ADCStatus_t ADC_ReadAllChannels(void);
float ADC_ConvertToVoltage(u16 raw_value);
u16 ADC_GetRawValue(u8 channel);
float ADC_GetVoltage(u8 channel);
ADCStatus_t ADC_GetStatus(void);
void ADC_StartConversion(void);
u8 ADC_IsConversionComplete(void);
void ADC_ProcessConversion(void);

// 滤波函数
u16 ADC_FilterValue(u8 channel, u16 new_value);
void ADC_ResetFilter(u8 channel);

// 校准函数
void ADC_Calibrate(void);
float ADC_GetCalibratedVoltage(u8 channel);

// 全局变量声明
extern ADCData_t g_adc_data;
extern ADC_HandleTypeDef hadc1;

#endif /* __ADC_H */
