#ifndef __SYSTEM_TEST_H
#define __SYSTEM_TEST_H

#include "sys.h"

// 测试结果定义
typedef enum {
    TEST_PASS = 0,
    TEST_FAIL,
    TEST_SKIP,
    TEST_TIMEOUT
} TestResult_t;

// 测试统计结构
typedef struct {
    u16 total_tests;
    u16 passed_tests;
    u16 failed_tests;
    u16 skipped_tests;
    u32 start_time;
    u32 end_time;
} TestStats_t;

// 测试函数声明
void SystemTest_RunAll(void);
void SystemTest_PrintResults(void);

// 模块测试函数
TestResult_t Test_StateMachine(void);
TestResult_t Test_Ultrasonic(void);
TestResult_t Test_ADC(void);
TestResult_t Test_UartExt(void);
TestResult_t Test_DisplayManager(void);
TestResult_t Test_Integration(void);

// 辅助测试函数
TestResult_t Test_BasicHardware(void);
TestResult_t Test_MemorySystem(void);
TestResult_t Test_TimingSystem(void);

// 测试工具函数
void Test_Assert(u8 condition, const char* test_name);
void Test_PrintHeader(const char* test_name);
void Test_PrintResult(const char* test_name, TestResult_t result);
u8 Test_WaitForCondition(u8 (*condition_func)(void), u32 timeout_ms);

// 全局测试统计
extern TestStats_t g_test_stats;

#endif /* __SYSTEM_TEST_H */
