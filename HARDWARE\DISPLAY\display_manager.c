#include "display_manager.h"
#include "delay.h"
#include "uart_ext.h"
#include "text.h"
#include <stdio.h>
#include <string.h>

// 全局显示管理器
DisplayManager_t g_display_manager;
GraphData_t g_ultrasonic_graph;
GraphData_t g_adc_graph[ADC_CHANNEL_COUNT];

// 显示模式名称
static const char* display_mode_names[] = {
    "主界面",
    "调试模式", 
    "设置界面",
    "图形显示"
};

/**
 * @brief 显示管理器初始化
 */
void DisplayManager_Init(void)
{
    // 初始化显示管理器
    g_display_manager.current_mode = DISPLAY_MODE_MAIN;
    g_display_manager.status = DISPLAY_STATUS_OK;
    g_display_manager.last_update_time = 0;
    g_display_manager.update_interval = 500; // 500ms更新间隔
    g_display_manager.auto_update = 1;
    g_display_manager.backlight_level = 80;
    
    // 设置颜色
    g_display_manager.background_color = WHITE;
    g_display_manager.text_color = BLACK;
    g_display_manager.highlight_color = BLUE;
    g_display_manager.error_color = RED;
    
    // 初始化图形数据
    DisplayManager_InitGraph(&g_ultrasonic_graph, 30, 280, 180, 80);
    for (u8 i = 0; i < ADC_CHANNEL_COUNT; i++) {
        DisplayManager_InitGraph(&g_adc_graph[i], 30 + i * 50, 380, 40, 60);
    }
    
    // 清屏并显示初始界面
    DisplayManager_ClearScreen();
    DisplayManager_ShowMainScreen();
    
    UART_INFO("Display Manager: Initialized successfully\r\n");
}

/**
 * @brief 显示管理器更新
 */
void DisplayManager_Update(void)
{
    u32 current_time = HAL_GetTick();
    
    // 检查是否需要更新
    if (!g_display_manager.auto_update || 
        (current_time - g_display_manager.last_update_time) < g_display_manager.update_interval) {
        return;
    }
    
    g_display_manager.status = DISPLAY_STATUS_UPDATING;
    
    // 根据当前模式更新显示
    switch (g_display_manager.current_mode) {
        case DISPLAY_MODE_MAIN:
            DisplayManager_ShowMainScreen();
            break;
            
        case DISPLAY_MODE_DEBUG:
            DisplayManager_ShowDebugScreen();
            break;
            
        case DISPLAY_MODE_SETTINGS:
            DisplayManager_ShowSettingsScreen();
            break;
            
        case DISPLAY_MODE_GRAPH:
            DisplayManager_ShowGraphScreen();
            break;
            
        default:
            break;
    }
    
    g_display_manager.last_update_time = current_time;
    g_display_manager.status = DISPLAY_STATUS_OK;
}

/**
 * @brief 设置显示模式
 * @param mode: 显示模式
 */
void DisplayManager_SetMode(DisplayMode_t mode)
{
    if (mode >= DISPLAY_MODE_MAX) return;
    
    if (g_display_manager.current_mode != mode) {
        g_display_manager.current_mode = mode;
        DisplayManager_ClearScreen();
        UART_INFO("Display mode changed to: %s\r\n", display_mode_names[mode]);
    }
}

/**
 * @brief 获取当前显示模式
 * @return 当前显示模式
 */
DisplayMode_t DisplayManager_GetMode(void)
{
    return g_display_manager.current_mode;
}

/**
 * @brief 设置更新间隔
 * @param interval_ms: 更新间隔(毫秒)
 */
void DisplayManager_SetUpdateInterval(u32 interval_ms)
{
    g_display_manager.update_interval = interval_ms;
}

/**
 * @brief 启用/禁用自动更新
 * @param enable: 1-启用, 0-禁用
 */
void DisplayManager_EnableAutoUpdate(u8 enable)
{
    g_display_manager.auto_update = enable;
}

/**
 * @brief 显示标题
 */
void DisplayManager_ShowTitle(void)
{
    POINT_COLOR = g_display_manager.highlight_color;
    Show_Str(DISPLAY_TITLE_X, DISPLAY_TITLE_Y, 200, DISPLAY_LARGE_FONT, 
             "STM32超声波测距系统", DISPLAY_LARGE_FONT, 0);
}

/**
 * @brief 显示系统状态
 */
void DisplayManager_ShowSystemStatus(void)
{
    char status_str[50];
    StateMachineState_t state = StateMachine_GetCurrentState(&g_state_machine);
    
    POINT_COLOR = g_display_manager.text_color;
    
    // 显示状态机状态
    switch (state) {
        case STATE_IDLE:
            strcpy(status_str, "系统状态: 空闲");
            break;
        case STATE_INIT:
            strcpy(status_str, "系统状态: 初始化");
            break;
        case STATE_RUNNING:
            strcpy(status_str, "系统状态: 运行中");
            break;
        case STATE_MEASURING:
            strcpy(status_str, "系统状态: 测量中");
            break;
        case STATE_DISPLAYING:
            strcpy(status_str, "系统状态: 显示中");
            break;
        case STATE_ERROR:
            strcpy(status_str, "系统状态: 错误");
            POINT_COLOR = g_display_manager.error_color;
            break;
        default:
            strcpy(status_str, "系统状态: 未知");
            break;
    }
    
    Show_Str(DISPLAY_STATUS_X, DISPLAY_STATUS_Y, 200, DISPLAY_FONT_SIZE, 
             status_str, DISPLAY_FONT_SIZE, 0);
}

/**
 * @brief 显示超声波数据
 */
void DisplayManager_ShowUltrasonicData(void)
{
    char distance_str[50];
    char status_str[30];
    float distance = Ultrasonic_GetDistance_CM();
    UltrasonicStatus_t status = Ultrasonic_GetStatus();
    
    POINT_COLOR = g_display_manager.text_color;
    
    // 显示距离
    if (status == ULTRASONIC_OK) {
        snprintf(distance_str, sizeof(distance_str), "距离: %.2f cm", distance);
        POINT_COLOR = g_display_manager.text_color;
    } else {
        switch (status) {
            case ULTRASONIC_TIMEOUT:
                strcpy(distance_str, "距离: 超时");
                break;
            case ULTRASONIC_OUT_OF_RANGE:
                strcpy(distance_str, "距离: 超出范围");
                break;
            default:
                strcpy(distance_str, "距离: 错误");
                break;
        }
        POINT_COLOR = g_display_manager.error_color;
    }
    
    Show_Str(DISPLAY_ULTRASONIC_X, DISPLAY_ULTRASONIC_Y, 200, DISPLAY_FONT_SIZE, 
             distance_str, DISPLAY_FONT_SIZE, 0);
    
    // 显示状态
    POINT_COLOR = g_display_manager.text_color;
    snprintf(status_str, sizeof(status_str), "状态: %s", 
             (status == ULTRASONIC_OK) ? "正常" : "异常");
    Show_Str(DISPLAY_ULTRASONIC_X, DISPLAY_ULTRASONIC_Y + DISPLAY_LINE_HEIGHT, 
             200, DISPLAY_FONT_SIZE, status_str, DISPLAY_FONT_SIZE, 0);
    
    // 添加数据到图形
    if (status == ULTRASONIC_OK) {
        DisplayManager_AddGraphData(&g_ultrasonic_graph, distance);
    }
}

/**
 * @brief 显示ADC数据
 */
void DisplayManager_ShowADCData(void)
{
    char adc_str[60];
    
    POINT_COLOR = g_display_manager.text_color;
    Show_Str(DISPLAY_ADC_X, DISPLAY_ADC_Y, 200, DISPLAY_FONT_SIZE, 
             "ADC通道数据:", DISPLAY_FONT_SIZE, 0);
    
    for (u8 i = 0; i < ADC_CHANNEL_COUNT; i++) {
        u16 raw_value = ADC_GetRawValue(i);
        float voltage = ADC_GetVoltage(i);
        
        snprintf(adc_str, sizeof(adc_str), "CH%d: %d (%.3fV)", i, raw_value, voltage);
        Show_Str(DISPLAY_ADC_X, DISPLAY_ADC_Y + (i + 1) * DISPLAY_LINE_HEIGHT, 
                 200, DISPLAY_FONT_SIZE, adc_str, DISPLAY_FONT_SIZE, 0);
        
        // 添加数据到图形
        DisplayManager_AddGraphData(&g_adc_graph[i], voltage);
    }
}

/**
 * @brief 显示时间信息
 */
void DisplayManager_ShowTimeInfo(void)
{
    char time_str[50];
    u32 system_time = HAL_GetTick();
    
    POINT_COLOR = g_display_manager.text_color;
    snprintf(time_str, sizeof(time_str), "运行时间: %d.%03d 秒", 
             system_time / 1000, system_time % 1000);
    Show_Str(DISPLAY_TIME_X, DISPLAY_TIME_Y, 200, DISPLAY_FONT_SIZE, 
             time_str, DISPLAY_FONT_SIZE, 0);
}

/**
 * @brief 显示主界面
 */
void DisplayManager_ShowMainScreen(void)
{
    DisplayManager_ShowTitle();
    DisplayManager_ShowSystemStatus();
    DisplayManager_ShowUltrasonicData();
    DisplayManager_ShowADCData();
    DisplayManager_ShowTimeInfo();
}

/**
 * @brief 显示调试界面
 */
void DisplayManager_ShowDebugScreen(void)
{
    char debug_str[100];
    
    POINT_COLOR = g_display_manager.highlight_color;
    Show_Str(30, 30, 200, DISPLAY_LARGE_FONT, "调试信息", DISPLAY_LARGE_FONT, 0);
    
    POINT_COLOR = g_display_manager.text_color;
    
    // 显示内存使用情况
    snprintf(debug_str, sizeof(debug_str), "系统时钟: %d MHz", SystemCoreClock / 1000000);
    Show_Str(30, 70, 200, DISPLAY_FONT_SIZE, debug_str, DISPLAY_FONT_SIZE, 0);
    
    // 显示UART统计
    snprintf(debug_str, sizeof(debug_str), "UART TX: %d bytes", UartExt_GetTxCount());
    Show_Str(30, 90, 200, DISPLAY_FONT_SIZE, debug_str, DISPLAY_FONT_SIZE, 0);
    
    snprintf(debug_str, sizeof(debug_str), "UART RX: %d bytes", UartExt_GetRxCount());
    Show_Str(30, 110, 200, DISPLAY_FONT_SIZE, debug_str, DISPLAY_FONT_SIZE, 0);
    
    // 显示状态机信息
    DisplayManager_ShowSystemStatus();
}

/**
 * @brief 清屏
 */
void DisplayManager_ClearScreen(void)
{
    LCD_Clear(g_display_manager.background_color);
}

/**
 * @brief 初始化图形数据
 * @param graph: 图形数据指针
 * @param x, y: 图形位置
 * @param width, height: 图形尺寸
 */
void DisplayManager_InitGraph(GraphData_t* graph, u16 x, u16 y, u16 width, u16 height)
{
    if (graph == NULL) return;
    
    memset(graph->data_buffer, 0, sizeof(graph->data_buffer));
    graph->buffer_index = 0;
    graph->min_value = 0.0f;
    graph->max_value = 100.0f;
    graph->graph_x = x;
    graph->graph_y = y;
    graph->graph_width = width;
    graph->graph_height = height;
}

/**
 * @brief 添加图形数据
 * @param graph: 图形数据指针
 * @param value: 数据值
 */
void DisplayManager_AddGraphData(GraphData_t* graph, float value)
{
    if (graph == NULL) return;
    
    graph->data_buffer[graph->buffer_index] = value;
    graph->buffer_index = (graph->buffer_index + 1) % 240;
    
    // 更新最值
    if (value < graph->min_value) graph->min_value = value;
    if (value > graph->max_value) graph->max_value = value;
}
