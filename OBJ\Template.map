Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to sys.o(i.Stm32_Clock_Init) for Stm32_Clock_Init
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to usart.o(i.uart_init) for uart_init
    main.o(i.main) refers to led.o(i.LED_Init) for LED_Init
    main.o(i.main) refers to key.o(i.KEY_Init) for KEY_Init
    main.o(i.main) refers to lcd.o(i.LCD_Init) for LCD_Init
    main.o(i.main) refers to sram.o(i.SRAM_Init) for SRAM_Init
    main.o(i.main) refers to w25qxx.o(i.W25QXX_Init) for W25QXX_Init
    main.o(i.main) refers to malloc.o(i.my_mem_init) for my_mem_init
    main.o(i.main) refers to exfuns.o(i.exfuns_init) for exfuns_init
    main.o(i.main) refers to ff.o(i.f_mount) for f_mount
    main.o(i.main) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    main.o(i.main) refers to lcd.o(i.LCD_ShowString) for LCD_ShowString
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to lcd.o(i.LCD_Fill) for LCD_Fill
    main.o(i.main) refers to sdio_sdcard.o(i.SD_Init) for SD_Init
    main.o(i.main) refers to fontupd.o(i.update_font) for update_font
    main.o(i.main) refers to fontupd.o(i.font_init) for font_init
    main.o(i.main) refers to text.o(i.Show_Str) for Show_Str
    main.o(i.main) refers to lcd.o(i.LCD_ShowNum) for LCD_ShowNum
    main.o(i.main) refers to text.o(i.Show_Font) for Show_Font
    main.o(i.main) refers to key.o(i.KEY_Scan) for KEY_Scan
    main.o(i.main) refers to usmart_config.o(.data) for usmart_dev
    main.o(i.main) refers to exfuns.o(.data) for fs
    main.o(i.main) refers to lcd.o(.data) for POINT_COLOR
    stm32f4xx_it.o(i.HardFault_Handler) refers to noretval__2printf.o(.text) for __2printf
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to usmart.o(i.TIM4_IRQHandler) for TIM4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal_msp.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.NVIC_SetPriority) for NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.NVIC_SetPriority) for NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_usart.o(i.HAL_USART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32f4xx_hal_usart.o(i.HAL_USART_AbortCpltCallback) for HAL_USART_AbortCpltCallback
    stm32f4xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32f4xx_hal_usart.o(i.USART_DMATxAbortCallback) for USART_DMATxAbortCallback
    stm32f4xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32f4xx_hal_usart.o(i.USART_DMARxAbortCallback) for USART_DMARxAbortCallback
    stm32f4xx_hal_usart.o(i.HAL_USART_DMAStop) refers to stm32f4xx_hal_usart.o(i.USART_EndTxTransfer) for USART_EndTxTransfer
    stm32f4xx_hal_usart.o(i.HAL_USART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_usart.o(i.HAL_USART_DMAStop) refers to stm32f4xx_hal_usart.o(i.USART_EndRxTransfer) for USART_EndRxTransfer
    stm32f4xx_hal_usart.o(i.HAL_USART_DeInit) refers to stm32f4xx_hal_usart.o(i.HAL_USART_MspDeInit) for HAL_USART_MspDeInit
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.USART_TransmitReceive_IT) for USART_TransmitReceive_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.USART_EndRxTransfer) for USART_EndRxTransfer
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.USART_Receive_IT) for USART_Receive_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.HAL_USART_ErrorCallback) for HAL_USART_ErrorCallback
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.USART_Transmit_IT) for USART_Transmit_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.HAL_USART_TxCpltCallback) for HAL_USART_TxCpltCallback
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.USART_DMAAbortOnError) for USART_DMAAbortOnError
    stm32f4xx_hal_usart.o(i.HAL_USART_Init) refers to stm32f4xx_hal_usart.o(i.HAL_USART_MspInit) for HAL_USART_MspInit
    stm32f4xx_hal_usart.o(i.HAL_USART_Init) refers to stm32f4xx_hal_usart.o(i.USART_SetConfig) for USART_SetConfig
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive) refers to stm32f4xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMAReceiveCplt) for USART_DMAReceiveCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMARxHalfCplt) for USART_DMARxHalfCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit) refers to stm32f4xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive) refers to stm32f4xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMAReceiveCplt) for USART_DMAReceiveCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMARxHalfCplt) for USART_DMARxHalfCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMATransmitCplt) for USART_DMATransmitCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMATxHalfCplt) for USART_DMATxHalfCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMATransmitCplt) for USART_DMATransmitCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMATxHalfCplt) for USART_DMATxHalfCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32f4xx_hal_usart.o(i.USART_DMAAbortOnError) refers to stm32f4xx_hal_usart.o(i.HAL_USART_ErrorCallback) for HAL_USART_ErrorCallback
    stm32f4xx_hal_usart.o(i.USART_DMAError) refers to stm32f4xx_hal_usart.o(i.USART_EndTxTransfer) for USART_EndTxTransfer
    stm32f4xx_hal_usart.o(i.USART_DMAError) refers to stm32f4xx_hal_usart.o(i.USART_EndRxTransfer) for USART_EndRxTransfer
    stm32f4xx_hal_usart.o(i.USART_DMAError) refers to stm32f4xx_hal_usart.o(i.HAL_USART_ErrorCallback) for HAL_USART_ErrorCallback
    stm32f4xx_hal_usart.o(i.USART_DMAReceiveCplt) refers to stm32f4xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback) for HAL_USART_TxRxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMAReceiveCplt) refers to stm32f4xx_hal_usart.o(i.HAL_USART_RxCpltCallback) for HAL_USART_RxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMARxAbortCallback) refers to stm32f4xx_hal_usart.o(i.HAL_USART_AbortCpltCallback) for HAL_USART_AbortCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMARxHalfCplt) refers to stm32f4xx_hal_usart.o(i.HAL_USART_RxHalfCpltCallback) for HAL_USART_RxHalfCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMATransmitCplt) refers to stm32f4xx_hal_usart.o(i.HAL_USART_TxCpltCallback) for HAL_USART_TxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMATxAbortCallback) refers to stm32f4xx_hal_usart.o(i.HAL_USART_AbortCpltCallback) for HAL_USART_AbortCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMATxHalfCplt) refers to stm32f4xx_hal_usart.o(i.HAL_USART_TxHalfCpltCallback) for HAL_USART_TxHalfCpltCallback
    stm32f4xx_hal_usart.o(i.USART_Receive_IT) refers to stm32f4xx_hal_usart.o(i.HAL_USART_RxCpltCallback) for HAL_USART_RxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_usart.o(i.USART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_usart.o(i.USART_TransmitReceive_IT) refers to stm32f4xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback) for HAL_USART_TxRxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutationCallback) for HAL_TIMEx_CommutationCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchronization) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchronization_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutationEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutationEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutationCallback) for HAL_TIMEx_CommutationCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Init) refers to spi.o(i.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) for HAL_SPI_TransmitReceive_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BITCRC) for SPI_2linesRxISR_16BITCRC
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BITCRC) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BITCRC) for SPI_2linesRxISR_8BITCRC
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BITCRC) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAError) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_16BITCRC) for SPI_RxISR_16BITCRC
    stm32f4xx_hal_spi.o(i.SPI_RxISR_16BITCRC) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_8BITCRC) for SPI_RxISR_8BITCRC
    stm32f4xx_hal_spi.o(i.SPI_RxISR_8BITCRC) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_MspDeInit) for HAL_SRAM_MspDeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_DeInit) for FSMC_NORSRAM_DeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to lcd.o(i.HAL_SRAM_MspInit) for HAL_SRAM_MspInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Init) for FSMC_NORSRAM_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Timing_Init) for FSMC_NORSRAM_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Extended_Timing_Init) for FSMC_NORSRAM_Extended_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_WriteOperation_Disable) for FSMC_NORSRAM_WriteOperation_Disable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_WriteOperation_Enable) for FSMC_NORSRAM_WriteOperation_Enable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_ll_fsmc.o(i.FSMC_NAND_GetECC) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32f4xx_hal_sd.o(i.HAL_SD_AbortCallback) for HAL_SD_AbortCallback
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32f4xx_hal_sd.o(i.SD_DMATxAbort) for SD_DMATxAbort
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32f4xx_hal_sd.o(i.SD_DMARxAbort) for SD_DMARxAbort
    stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32f4xx_hal_sd.o(i.SD_WideBus_Enable) for SD_WideBus_Enable
    stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32f4xx_hal_sd.o(i.SD_WideBus_Disable) for SD_WideBus_Disable
    stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_Init) for SDIO_Init
    stm32f4xx_hal_sd.o(i.HAL_SD_DeInit) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_PowerState_OFF) for SDIO_PowerState_OFF
    stm32f4xx_hal_sd.o(i.HAL_SD_DeInit) refers to stm32f4xx_hal_sd.o(i.HAL_SD_MspDeInit) for HAL_SD_MspDeInit
    stm32f4xx_hal_sd.o(i.HAL_SD_Erase) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_hal_sd.o(i.HAL_SD_Erase) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseStartAdd) for SDMMC_CmdSDEraseStartAdd
    stm32f4xx_hal_sd.o(i.HAL_SD_Erase) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseEndAdd) for SDMMC_CmdSDEraseEndAdd
    stm32f4xx_hal_sd.o(i.HAL_SD_Erase) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdErase) for SDMMC_CmdErase
    stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus) for SDMMC_CmdSendStatus
    stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_hal_sd.o(i.HAL_SD_GetCardStatus) refers to stm32f4xx_hal_sd.o(i.SD_SendSDStatus) for SD_SendSDStatus
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ErrorCallback) for HAL_SD_ErrorCallback
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_hal_sd.o(i.HAL_SD_RxCpltCallback) for HAL_SD_RxCpltCallback
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_hal_sd.o(i.HAL_SD_TxCpltCallback) for HAL_SD_TxCpltCallback
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_WriteFIFO) for SDIO_WriteFIFO
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ReadFIFO) for SDIO_ReadFIFO
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_hal_sd.o(i.SD_DMATxAbort) for SD_DMATxAbort
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_hal_sd.o(i.SD_DMARxAbort) for SD_DMARxAbort
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_hal_sd.o(i.HAL_SD_AbortCallback) for HAL_SD_AbortCallback
    stm32f4xx_hal_sd.o(i.HAL_SD_Init) refers to sdio_sdcard.o(i.HAL_SD_MspInit) for HAL_SD_MspInit
    stm32f4xx_hal_sd.o(i.HAL_SD_Init) refers to stm32f4xx_hal_sd.o(i.HAL_SD_InitCard) for HAL_SD_InitCard
    stm32f4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_Init) for SDIO_Init
    stm32f4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_PowerState_ON) for SDIO_PowerState_ON
    stm32f4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    stm32f4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32f4xx_hal_sd.o(i.SD_PowerON) for SD_PowerON
    stm32f4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32f4xx_hal_sd.o(i.SD_InitCard) for SD_InitCard
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock) for SDMMC_CmdReadMultiBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock) for SDMMC_CmdReadSingleBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ReadFIFO) for SDIO_ReadFIFO
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock) for SDMMC_CmdReadMultiBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock) for SDMMC_CmdReadSingleBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32f4xx_hal_sd.o(i.SD_DMAReceiveCplt) for SD_DMAReceiveCplt
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32f4xx_hal_sd.o(i.SD_DMAError) for SD_DMAError
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock) for SDMMC_CmdReadMultiBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock) for SDMMC_CmdReadSingleBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock) for SDMMC_CmdWriteMultiBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock) for SDMMC_CmdWriteSingleBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_WriteFIFO) for SDIO_WriteFIFO
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock) for SDMMC_CmdWriteMultiBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock) for SDMMC_CmdWriteSingleBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32f4xx_hal_sd.o(i.SD_DMATransmitCplt) for SD_DMATransmitCplt
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32f4xx_hal_sd.o(i.SD_DMAError) for SD_DMAError
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock) for SDMMC_CmdWriteMultiBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock) for SDMMC_CmdWriteSingleBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.SD_DMAError) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    stm32f4xx_hal_sd.o(i.SD_DMAError) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.SD_DMAError) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ErrorCallback) for HAL_SD_ErrorCallback
    stm32f4xx_hal_sd.o(i.SD_DMAReceiveCplt) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.SD_DMAReceiveCplt) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ErrorCallback) for HAL_SD_ErrorCallback
    stm32f4xx_hal_sd.o(i.SD_DMAReceiveCplt) refers to stm32f4xx_hal_sd.o(i.HAL_SD_RxCpltCallback) for HAL_SD_RxCpltCallback
    stm32f4xx_hal_sd.o(i.SD_DMARxAbort) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    stm32f4xx_hal_sd.o(i.SD_DMARxAbort) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.SD_DMARxAbort) refers to stm32f4xx_hal_sd.o(i.HAL_SD_AbortCallback) for HAL_SD_AbortCallback
    stm32f4xx_hal_sd.o(i.SD_DMARxAbort) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ErrorCallback) for HAL_SD_ErrorCallback
    stm32f4xx_hal_sd.o(i.SD_DMATxAbort) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    stm32f4xx_hal_sd.o(i.SD_DMATxAbort) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.SD_DMATxAbort) refers to stm32f4xx_hal_sd.o(i.HAL_SD_AbortCallback) for HAL_SD_AbortCallback
    stm32f4xx_hal_sd.o(i.SD_DMATxAbort) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ErrorCallback) for HAL_SD_ErrorCallback
    stm32f4xx_hal_sd.o(i.SD_FindSCR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sd.o(i.SD_FindSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32f4xx_hal_sd.o(i.SD_FindSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) for SDMMC_CmdAppCommand
    stm32f4xx_hal_sd.o(i.SD_FindSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.SD_FindSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendSCR) for SDMMC_CmdSendSCR
    stm32f4xx_hal_sd.o(i.SD_FindSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ReadFIFO) for SDIO_ReadFIFO
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetPowerState) for SDIO_GetPowerState
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID) for SDMMC_CmdSendCID
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd) for SDMMC_CmdSetRelAdd
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD) for SDMMC_CmdSendCSD
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardCSD) for HAL_SD_GetCardCSD
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel) for SDMMC_CmdSelDesel
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_Init) for SDIO_Init
    stm32f4xx_hal_sd.o(i.SD_PowerON) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState) for SDMMC_CmdGoIdleState
    stm32f4xx_hal_sd.o(i.SD_PowerON) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond) for SDMMC_CmdOperCond
    stm32f4xx_hal_sd.o(i.SD_PowerON) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) for SDMMC_CmdAppCommand
    stm32f4xx_hal_sd.o(i.SD_PowerON) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand) for SDMMC_CmdAppOperCommand
    stm32f4xx_hal_sd.o(i.SD_PowerON) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) for SDMMC_CmdAppCommand
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStatusRegister) for SDMMC_CmdStatusRegister
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ReadFIFO) for SDIO_ReadFIFO
    stm32f4xx_hal_sd.o(i.SD_WideBus_Disable) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_hal_sd.o(i.SD_WideBus_Disable) refers to stm32f4xx_hal_sd.o(i.SD_FindSCR) for SD_FindSCR
    stm32f4xx_hal_sd.o(i.SD_WideBus_Disable) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) for SDMMC_CmdAppCommand
    stm32f4xx_hal_sd.o(i.SD_WideBus_Disable) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth) for SDMMC_CmdBusWidth
    stm32f4xx_hal_sd.o(i.SD_WideBus_Enable) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_hal_sd.o(i.SD_WideBus_Enable) refers to stm32f4xx_hal_sd.o(i.SD_FindSCR) for SD_FindSCR
    stm32f4xx_hal_sd.o(i.SD_WideBus_Enable) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) for SDMMC_CmdAppCommand
    stm32f4xx_hal_sd.o(i.SD_WideBus_Enable) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth) for SDMMC_CmdBusWidth
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3) for SDMMC_GetCmdResp3
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdErase) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdErase) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdEraseEndAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdEraseEndAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdEraseStartAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdEraseStartAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOpCondition) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOpCondition) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3) for SDMMC_GetCmdResp3
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseEndAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseEndAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseStartAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseStartAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2) for SDMMC_GetCmdResp2
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2) for SDMMC_GetCmdResp2
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6) for SDMMC_GetCmdResp6
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStatusRegister) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStatusRegister) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSwitch) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSwitch) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    delay.o(i.delay_init) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig) for HAL_SYSTICK_CLKSourceConfig
    delay.o(i.delay_init) refers to delay.o(.data) for .data
    delay.o(i.delay_ms) refers to delay.o(i.delay_us) for delay_us
    delay.o(i.delay_us) refers to delay.o(.data) for .data
    sys.o(i.Stm32_Clock_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    sys.o(i.Stm32_Clock_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    sys.o(i.Stm32_Clock_Init) refers to stm32f4xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    usart.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.rrx_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.HAL_UART_MspInit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_RxCpltCallback) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.data) for .data
    usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for .bss
    usart.o(i.USART1_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_GetState) for HAL_UART_GetState
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for .bss
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for .data
    usart.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.uart_init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usart.o(i.uart_init) refers to usart.o(.bss) for .bss
    usart.o(i.uart_init) refers to usart.o(.data) for .data
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    led.o(i.LED_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    led.o(i.LED_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd.o(i.HAL_SRAM_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(i.LCD_Clear) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_Clear) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_Clear) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_Color_Fill) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_Color_Fill) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_DisplayOff) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_DisplayOff) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_DisplayOn) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_DisplayOn) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_Display_Dir) refers to lcd.o(i.LCD_Scan_Dir) for LCD_Scan_Dir
    lcd.o(i.LCD_Display_Dir) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_DrawLine) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_DrawPoint) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_DrawPoint) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_DrawPoint) refers to lcd.o(.data) for .data
    lcd.o(i.LCD_DrawRectangle) refers to lcd.o(i.LCD_DrawLine) for LCD_DrawLine
    lcd.o(i.LCD_Draw_Circle) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_Fast_DrawPoint) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_Fast_DrawPoint) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_Fast_DrawPoint) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_Fill) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_Fill) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_Init) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lcd.o(i.LCD_Init) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lcd.o(i.LCD_Init) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    lcd.o(i.LCD_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(i.LCD_Init) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) for HAL_SRAM_Init
    lcd.o(i.LCD_Init) refers to delay.o(i.delay_ms) for delay_ms
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_RD_DATA) for LCD_RD_DATA
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_Init) refers to noretval__2printf.o(.text) for __2printf
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_Init) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_Init) refers to delay.o(i.delay_us) for delay_us
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_SSD_BackLightSet) for LCD_SSD_BackLightSet
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_Display_Dir) for LCD_Display_Dir
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(i.LCD_RD_DATA) for LCD_RD_DATA
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_ReadReg) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_ReadReg) refers to delay.o(i.delay_us) for delay_us
    lcd.o(i.LCD_ReadReg) refers to lcd.o(i.LCD_RD_DATA) for LCD_RD_DATA
    lcd.o(i.LCD_SSD_BackLightSet) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_SSD_BackLightSet) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_SSD_BackLightSet) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    lcd.o(i.LCD_SSD_BackLightSet) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    lcd.o(i.LCD_SSD_BackLightSet) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    lcd.o(i.LCD_Scan_Dir) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_Scan_Dir) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_Scan_Dir) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_Scan_Dir) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_SetCursor) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_SetCursor) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_SetCursor) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_Set_Window) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_Set_Window) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_Set_Window) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_ShowChar) refers to lcd.o(i.LCD_Fast_DrawPoint) for LCD_Fast_DrawPoint
    lcd.o(i.LCD_ShowChar) refers to lcd.o(.constdata) for .constdata
    lcd.o(i.LCD_ShowChar) refers to lcd.o(.data) for .data
    lcd.o(i.LCD_ShowChar) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_ShowNum) refers to lcd.o(i.LCD_Pow) for LCD_Pow
    lcd.o(i.LCD_ShowNum) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCD_ShowString) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCD_ShowxNum) refers to lcd.o(i.LCD_Pow) for LCD_Pow
    lcd.o(i.LCD_ShowxNum) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCD_WriteRAM_Prepare) refers to lcd.o(.bss) for .bss
    key.o(i.KEY_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    key.o(i.KEY_Scan) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key.o(i.KEY_Scan) refers to delay.o(i.delay_ms) for delay_ms
    key.o(i.KEY_Scan) refers to key.o(.data) for .data
    sram.o(i.SRAM_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    sram.o(i.SRAM_Init) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) for HAL_SRAM_Init
    sram.o(i.SRAM_Init) refers to sram.o(.bss) for .bss
    spi.o(i.HAL_SPI_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    spi.o(i.SPI1_Init) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    spi.o(i.SPI1_Init) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    spi.o(i.SPI1_Init) refers to spi.o(.bss) for .bss
    spi.o(i.SPI1_ReadWriteByte) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    spi.o(i.SPI1_ReadWriteByte) refers to spi.o(.bss) for .bss
    spi.o(i.SPI1_SetSpeed) refers to spi.o(.bss) for .bss
    w25qxx.o(i.W25QXX_Erase_Chip) refers to w25qxx.o(i.W25QXX_Write_Enable) for W25QXX_Write_Enable
    w25qxx.o(i.W25QXX_Erase_Chip) refers to w25qxx.o(i.W25QXX_Wait_Busy) for W25QXX_Wait_Busy
    w25qxx.o(i.W25QXX_Erase_Chip) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25qxx.o(i.W25QXX_Erase_Sector) refers to w25qxx.o(i.W25QXX_Write_Enable) for W25QXX_Write_Enable
    w25qxx.o(i.W25QXX_Erase_Sector) refers to w25qxx.o(i.W25QXX_Wait_Busy) for W25QXX_Wait_Busy
    w25qxx.o(i.W25QXX_Erase_Sector) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25qxx.o(i.W25QXX_Erase_Sector) refers to w25qxx.o(.data) for .data
    w25qxx.o(i.W25QXX_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    w25qxx.o(i.W25QXX_Init) refers to spi.o(i.SPI1_Init) for SPI1_Init
    w25qxx.o(i.W25QXX_Init) refers to spi.o(i.SPI1_SetSpeed) for SPI1_SetSpeed
    w25qxx.o(i.W25QXX_Init) refers to w25qxx.o(i.W25QXX_ReadID) for W25QXX_ReadID
    w25qxx.o(i.W25QXX_Init) refers to w25qxx.o(i.W25QXX_ReadSR) for W25QXX_ReadSR
    w25qxx.o(i.W25QXX_Init) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25qxx.o(i.W25QXX_Init) refers to w25qxx.o(.data) for .data
    w25qxx.o(i.W25QXX_PowerDown) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25qxx.o(i.W25QXX_PowerDown) refers to delay.o(i.delay_us) for delay_us
    w25qxx.o(i.W25QXX_Read) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25qxx.o(i.W25QXX_Read) refers to w25qxx.o(.data) for .data
    w25qxx.o(i.W25QXX_ReadID) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25qxx.o(i.W25QXX_ReadSR) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25qxx.o(i.W25QXX_WAKEUP) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25qxx.o(i.W25QXX_WAKEUP) refers to delay.o(i.delay_us) for delay_us
    w25qxx.o(i.W25QXX_Wait_Busy) refers to w25qxx.o(i.W25QXX_ReadSR) for W25QXX_ReadSR
    w25qxx.o(i.W25QXX_Write) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    w25qxx.o(i.W25QXX_Write) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    w25qxx.o(i.W25QXX_Write) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    w25qxx.o(i.W25QXX_Write) refers to noretval__2printf.o(.text) for __2printf
    w25qxx.o(i.W25QXX_Write) refers to w25qxx.o(i.W25QXX_Read) for W25QXX_Read
    w25qxx.o(i.W25QXX_Write) refers to w25qxx.o(i.W25QXX_Erase_Sector) for W25QXX_Erase_Sector
    w25qxx.o(i.W25QXX_Write) refers to w25qxx.o(i.W25QXX_Write_NoCheck) for W25QXX_Write_NoCheck
    w25qxx.o(i.W25QXX_Write) refers to w25qxx.o(.bss) for .bss
    w25qxx.o(i.W25QXX_Write_Disable) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25qxx.o(i.W25QXX_Write_Enable) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25qxx.o(i.W25QXX_Write_NoCheck) refers to w25qxx.o(i.W25QXX_Write_Page) for W25QXX_Write_Page
    w25qxx.o(i.W25QXX_Write_Page) refers to w25qxx.o(i.W25QXX_Write_Enable) for W25QXX_Write_Enable
    w25qxx.o(i.W25QXX_Write_Page) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25qxx.o(i.W25QXX_Write_Page) refers to w25qxx.o(i.W25QXX_Wait_Busy) for W25QXX_Wait_Busy
    w25qxx.o(i.W25QXX_Write_Page) refers to w25qxx.o(.data) for .data
    w25qxx.o(i.W25QXX_Write_SR) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    sdio_sdcard.o(i.HAL_SD_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    sdio_sdcard.o(i.HAL_SD_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    sdio_sdcard.o(i.SD_GetCardInfo) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardInfo) for HAL_SD_GetCardInfo
    sdio_sdcard.o(i.SD_GetCardInfo) refers to sdio_sdcard.o(.bss) for .bss
    sdio_sdcard.o(i.SD_GetCardState) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    sdio_sdcard.o(i.SD_GetCardState) refers to sdio_sdcard.o(.bss) for .bss
    sdio_sdcard.o(i.SD_Init) refers to stm32f4xx_hal_sd.o(i.HAL_SD_Init) for HAL_SD_Init
    sdio_sdcard.o(i.SD_Init) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardInfo) for HAL_SD_GetCardInfo
    sdio_sdcard.o(i.SD_Init) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) for HAL_SD_ConfigWideBusOperation
    sdio_sdcard.o(i.SD_Init) refers to sdio_sdcard.o(.bss) for .bss
    sdio_sdcard.o(i.SD_ReadDisk) refers to sys.o(.emb_text) for INTX_DISABLE
    sdio_sdcard.o(i.SD_ReadDisk) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) for HAL_SD_ReadBlocks
    sdio_sdcard.o(i.SD_ReadDisk) refers to sdio_sdcard.o(i.SD_GetCardState) for SD_GetCardState
    sdio_sdcard.o(i.SD_ReadDisk) refers to sdio_sdcard.o(.bss) for .bss
    sdio_sdcard.o(i.SD_WriteDisk) refers to sys.o(.emb_text) for INTX_DISABLE
    sdio_sdcard.o(i.SD_WriteDisk) refers to stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) for HAL_SD_WriteBlocks
    sdio_sdcard.o(i.SD_WriteDisk) refers to sdio_sdcard.o(i.SD_GetCardState) for SD_GetCardState
    sdio_sdcard.o(i.SD_WriteDisk) refers to sdio_sdcard.o(.bss) for .bss
    usmart.o(i.TIM4_IRQHandler) refers to usmart.o(.bss) for .bss
    usmart.o(i.TIM4_IRQHandler) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.Timer4_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usmart.o(i.Timer4_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usmart.o(i.Timer4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    usmart.o(i.Timer4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    usmart.o(i.Timer4_Init) refers to usmart.o(.bss) for .bss
    usmart.o(i.usmart_cmd_rec) refers to usmart_str.o(i.usmart_get_fname) for usmart_get_fname
    usmart.o(i.usmart_cmd_rec) refers to usmart_str.o(i.usmart_strcmp) for usmart_strcmp
    usmart.o(i.usmart_cmd_rec) refers to usmart_str.o(i.usmart_get_fparam) for usmart_get_fparam
    usmart.o(i.usmart_cmd_rec) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_exe) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usmart.o(i.usmart_exe) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    usmart.o(i.usmart_exe) refers to _printf_str.o(.text) for _printf_str
    usmart.o(i.usmart_exe) refers to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    usmart.o(i.usmart_exe) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usmart.o(i.usmart_exe) refers to _printf_dec.o(.text) for _printf_int_dec
    usmart.o(i.usmart_exe) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    usmart.o(i.usmart_exe) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    usmart.o(i.usmart_exe) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    usmart.o(i.usmart_exe) refers to _printf_pad.o(.text) for _printf_pre_padding
    usmart.o(i.usmart_exe) refers to usmart_str.o(i.usmart_get_fname) for usmart_get_fname
    usmart.o(i.usmart_exe) refers to noretval__2printf.o(.text) for __2printf
    usmart.o(i.usmart_exe) refers to usmart_str.o(i.usmart_get_parmpos) for usmart_get_parmpos
    usmart.o(i.usmart_exe) refers to usmart.o(i.usmart_reset_runtime) for usmart_reset_runtime
    usmart.o(i.usmart_exe) refers to usmart.o(i.usmart_get_runtime) for usmart_get_runtime
    usmart.o(i.usmart_exe) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_get_runtime) refers to usmart.o(.bss) for .bss
    usmart.o(i.usmart_get_runtime) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_init) refers to usmart.o(i.Timer4_Init) for Timer4_Init
    usmart.o(i.usmart_init) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_reset_runtime) refers to usmart.o(.bss) for .bss
    usmart.o(i.usmart_reset_runtime) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_scan) refers to usmart.o(i.usmart_sys_cmd_exe) for usmart_sys_cmd_exe
    usmart.o(i.usmart_scan) refers to noretval__2printf.o(.text) for __2printf
    usmart.o(i.usmart_scan) refers to usart.o(.data) for USART_RX_STA
    usmart.o(i.usmart_scan) refers to usart.o(.bss) for USART_RX_BUF
    usmart.o(i.usmart_scan) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_sys_cmd_exe) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usmart.o(i.usmart_sys_cmd_exe) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    usmart.o(i.usmart_sys_cmd_exe) refers to _printf_str.o(.text) for _printf_str
    usmart.o(i.usmart_sys_cmd_exe) refers to _printf_pad.o(.text) for _printf_pre_padding
    usmart.o(i.usmart_sys_cmd_exe) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    usmart.o(i.usmart_sys_cmd_exe) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    usmart.o(i.usmart_sys_cmd_exe) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    usmart.o(i.usmart_sys_cmd_exe) refers to _printf_dec.o(.text) for _printf_int_dec
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_str.o(i.usmart_get_cmdname) for usmart_get_cmdname
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_str.o(i.usmart_strcmp) for usmart_strcmp
    usmart.o(i.usmart_sys_cmd_exe) refers to noretval__2printf.o(.text) for __2printf
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart.o(.data) for .data
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_str.o(i.usmart_get_fname) for usmart_get_fname
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_str.o(i.usmart_get_aparm) for usmart_get_aparm
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_str.o(i.usmart_str2num) for usmart_str2num
    usmart.o(.data) refers to usmart.o(.conststring) for .conststring
    usmart_str.o(i.usmart_get_fname) refers to usmart_str.o(i.usmart_strcmp) for usmart_strcmp
    usmart_str.o(i.usmart_get_fname) refers to usmart_str.o(i.usmart_search_nextc) for usmart_search_nextc
    usmart_str.o(i.usmart_get_fparam) refers to usmart_str.o(i.usmart_get_aparm) for usmart_get_aparm
    usmart_str.o(i.usmart_get_fparam) refers to usmart_str.o(i.usmart_str2num) for usmart_str2num
    usmart_str.o(i.usmart_get_fparam) refers to usmart_str.o(i.usmart_get_parmpos) for usmart_get_parmpos
    usmart_str.o(i.usmart_get_fparam) refers to usmart_str.o(i.usmart_strlen) for usmart_strlen
    usmart_str.o(i.usmart_get_fparam) refers to usmart_str.o(i.usmart_strcopy) for usmart_strcopy
    usmart_str.o(i.usmart_get_fparam) refers to usmart_config.o(.data) for usmart_dev
    usmart_str.o(i.usmart_get_parmpos) refers to usmart_config.o(.data) for usmart_dev
    usmart_str.o(i.usmart_str2num) refers to usmart_str.o(i.usmart_pow) for usmart_pow
    usmart_config.o(.data) refers to usmart.o(i.read_addr) for read_addr
    usmart_config.o(.data) refers to usmart_config.o(.conststring) for .conststring
    usmart_config.o(.data) refers to usmart.o(i.write_addr) for write_addr
    usmart_config.o(.data) refers to w25qxx.o(i.W25QXX_Erase_Chip) for W25QXX_Erase_Chip
    usmart_config.o(.data) refers to fattester.o(i.mf_mount) for mf_mount
    usmart_config.o(.data) refers to fattester.o(i.mf_open) for mf_open
    usmart_config.o(.data) refers to fattester.o(i.mf_close) for mf_close
    usmart_config.o(.data) refers to fattester.o(i.mf_read) for mf_read
    usmart_config.o(.data) refers to fattester.o(i.mf_write) for mf_write
    usmart_config.o(.data) refers to fattester.o(i.mf_opendir) for mf_opendir
    usmart_config.o(.data) refers to fattester.o(i.mf_closedir) for mf_closedir
    usmart_config.o(.data) refers to fattester.o(i.mf_readdir) for mf_readdir
    usmart_config.o(.data) refers to fattester.o(i.mf_scan_files) for mf_scan_files
    usmart_config.o(.data) refers to fattester.o(i.mf_showfree) for mf_showfree
    usmart_config.o(.data) refers to fattester.o(i.mf_lseek) for mf_lseek
    usmart_config.o(.data) refers to fattester.o(i.mf_tell) for mf_tell
    usmart_config.o(.data) refers to fattester.o(i.mf_size) for mf_size
    usmart_config.o(.data) refers to fattester.o(i.mf_mkdir) for mf_mkdir
    usmart_config.o(.data) refers to fattester.o(i.mf_fmkfs) for mf_fmkfs
    usmart_config.o(.data) refers to fattester.o(i.mf_unlink) for mf_unlink
    usmart_config.o(.data) refers to fattester.o(i.mf_rename) for mf_rename
    usmart_config.o(.data) refers to fattester.o(i.mf_getlabel) for mf_getlabel
    usmart_config.o(.data) refers to fattester.o(i.mf_setlabel) for mf_setlabel
    usmart_config.o(.data) refers to fattester.o(i.mf_gets) for mf_gets
    usmart_config.o(.data) refers to fattester.o(i.mf_putc) for mf_putc
    usmart_config.o(.data) refers to fattester.o(i.mf_puts) for mf_puts
    usmart_config.o(.data) refers to usmart_config.o(.data) for usmart_nametab
    usmart_config.o(.data) refers to usmart.o(i.usmart_init) for usmart_init
    usmart_config.o(.data) refers to usmart.o(i.usmart_cmd_rec) for usmart_cmd_rec
    usmart_config.o(.data) refers to usmart.o(i.usmart_exe) for usmart_exe
    usmart_config.o(.data) refers to usmart.o(i.usmart_scan) for usmart_scan
    malloc.o(i.my_mem_free) refers to malloc.o(.data) for .data
    malloc.o(i.my_mem_free) refers to malloc.o(.constdata) for .constdata
    malloc.o(i.my_mem_init) refers to malloc.o(i.mymemset) for mymemset
    malloc.o(i.my_mem_init) refers to malloc.o(.constdata) for .constdata
    malloc.o(i.my_mem_init) refers to malloc.o(.data) for .data
    malloc.o(i.my_mem_malloc) refers to malloc.o(.data) for .data
    malloc.o(i.my_mem_malloc) refers to malloc.o(.constdata) for .constdata
    malloc.o(i.my_mem_perused) refers to malloc.o(.constdata) for .constdata
    malloc.o(i.my_mem_perused) refers to malloc.o(.data) for .data
    malloc.o(i.myfree) refers to malloc.o(i.my_mem_free) for my_mem_free
    malloc.o(i.myfree) refers to malloc.o(.data) for .data
    malloc.o(i.mymalloc) refers to malloc.o(i.my_mem_malloc) for my_mem_malloc
    malloc.o(i.mymalloc) refers to malloc.o(.data) for .data
    malloc.o(i.myrealloc) refers to malloc.o(i.my_mem_malloc) for my_mem_malloc
    malloc.o(i.myrealloc) refers to malloc.o(i.mymemcpy) for mymemcpy
    malloc.o(i.myrealloc) refers to malloc.o(i.myfree) for myfree
    malloc.o(i.myrealloc) refers to malloc.o(.data) for .data
    malloc.o(.data) refers to malloc.o(i.my_mem_init) for my_mem_init
    malloc.o(.data) refers to malloc.o(i.my_mem_perused) for my_mem_perused
    malloc.o(.data) refers to malloc.o(.bss) for mem1base
    malloc.o(.data) refers to malloc.o(.ARM.__AT_0x68000000) for mem2base
    malloc.o(.data) refers to malloc.o(.ARM.__AT_0x10000000) for mem3base
    malloc.o(.data) refers to malloc.o(.bss) for mem1mapbase
    malloc.o(.data) refers to malloc.o(.ARM.__AT_0x680F0000) for mem2mapbase
    malloc.o(.data) refers to malloc.o(.ARM.__AT_0x1000F000) for mem3mapbase
    ff.o(i.change_bitmap) refers to ff.o(i.move_window) for move_window
    ff.o(i.check_fs) refers to ff.o(i.move_window) for move_window
    ff.o(i.check_fs) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.check_fs) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.check_fs) refers to ff.o(i.mem_cmp) for mem_cmp
    ff.o(i.clmt_clust) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    ff.o(i.cmp_lfn) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.cmp_lfn) refers to mycc936.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.cmp_lfn) refers to ff.o(.constdata) for .constdata
    ff.o(i.create_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.create_chain) refers to ff.o(i.find_bitmap) for find_bitmap
    ff.o(i.create_chain) refers to ff.o(i.change_bitmap) for change_bitmap
    ff.o(i.create_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.create_name) refers to mycc936.o(i.ff_convert) for ff_convert
    ff.o(i.create_name) refers to ff.o(i.chk_chr) for chk_chr
    ff.o(i.create_name) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.create_name) refers to mycc936.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.create_xdir) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.create_xdir) refers to ff.o(i.xname_sum) for xname_sum
    ff.o(i.create_xdir) refers to ff.o(i.st_word) for st_word
    ff.o(i.dir_alloc) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_alloc) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_alloc) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_find) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_find) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_find) refers to ff.o(i.xname_sum) for xname_sum
    ff.o(i.dir_find) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.dir_find) refers to mycc936.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.dir_find) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.dir_find) refers to ff.o(i.cmp_lfn) for cmp_lfn
    ff.o(i.dir_find) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_find) refers to ff.o(i.mem_cmp) for mem_cmp
    ff.o(i.dir_find) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_next) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_next) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.dir_next) refers to ff.o(i.sync_window) for sync_window
    ff.o(i.dir_next) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_next) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.dir_read) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_read) refers to ff.o(i.load_xdir) for load_xdir
    ff.o(i.dir_read) refers to ff.o(i.pick_lfn) for pick_lfn
    ff.o(i.dir_read) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_read) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_register) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.dir_register) refers to ff.o(i.gen_numname) for gen_numname
    ff.o(i.dir_register) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.dir_register) refers to ff.o(i.dir_alloc) for dir_alloc
    ff.o(i.dir_register) refers to ff.o(i.fill_fat_chain) for fill_fat_chain
    ff.o(i.dir_register) refers to ff.o(i.load_obj_dir) for load_obj_dir
    ff.o(i.dir_register) refers to ff.o(i.st_qword) for st_qword
    ff.o(i.dir_register) refers to ff.o(i.store_xdir) for store_xdir
    ff.o(i.dir_register) refers to ff.o(i.create_xdir) for create_xdir
    ff.o(i.dir_register) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_register) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_register) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_register) refers to ff.o(i.st_word) for st_word
    ff.o(i.dir_register) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_register) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_register) refers to ff.o(.constdata) for .constdata
    ff.o(i.dir_remove) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_remove) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_remove) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_sdi) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_sdi) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_close) refers to ff.o(i.f_sync) for f_sync
    ff.o(i.f_close) refers to ff.o(i.validate) for validate
    ff.o(i.f_closedir) refers to ff.o(i.validate) for validate
    ff.o(i.f_getfree) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_getfree) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_getfree) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_getfree) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.f_getfree) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.f_getlabel) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_getlabel) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_getlabel) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_getlabel) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.f_getlabel) refers to mycc936.o(i.ff_convert) for ff_convert
    ff.o(i.f_getlabel) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_getlabel) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.f_gets) refers to ff.o(i.f_read) for f_read
    ff.o(i.f_lseek) refers to ff.o(i.validate) for validate
    ff.o(i.f_lseek) refers to ff.o(i.clmt_clust) for clmt_clust
    ff.o(i.f_lseek) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_lseek) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_lseek) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_lseek) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_lseek) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    ff.o(i.f_lseek) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_mkdir) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_mkdir) refers to diskio.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_mkdir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_mkdir) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_mkdir) refers to ff.o(i.sync_window) for sync_window
    ff.o(i.f_mkdir) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_mkdir) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_mkdir) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.f_mkdir) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.f_mkdir) refers to ff.o(i.st_clust) for st_clust
    ff.o(i.f_mkdir) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_mkdir) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_mkdir) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_mkdir) refers to diskio.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_mkdir) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_mkdir) refers to ff.o(i.store_xdir) for store_xdir
    ff.o(i.f_mkfs) refers to ff.o(i.get_ldnumber) for get_ldnumber
    ff.o(i.f_mkfs) refers to diskio.o(i.disk_initialize) for disk_initialize
    ff.o(i.f_mkfs) refers to diskio.o(i.disk_ioctl) for disk_ioctl
    ff.o(i.f_mkfs) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.f_mkfs) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.f_mkfs) refers to ff.o(i.st_word) for st_word
    ff.o(i.f_mkfs) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_mkfs) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_mkfs) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_mkfs) refers to ff.o(.data) for .data
    ff.o(i.f_mkfs) refers to ff.o(.constdata) for .constdata
    ff.o(i.f_mount) refers to ff.o(i.get_ldnumber) for get_ldnumber
    ff.o(i.f_mount) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_mount) refers to ff.o(.data) for .data
    ff.o(i.f_open) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_open) refers to diskio.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_open) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_open) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_open) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_open) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.f_open) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.f_open) refers to ff.o(i.st_clust) for st_clust
    ff.o(i.f_open) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_open) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.f_open) refers to ff.o(i.ld_qword) for ld_qword
    ff.o(i.f_open) refers to ff.o(i.st_qword) for st_qword
    ff.o(i.f_open) refers to ff.o(i.store_xdir) for store_xdir
    ff.o(i.f_open) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_open) refers to diskio.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_opendir) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_opendir) refers to diskio.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_opendir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_opendir) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.f_opendir) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.f_opendir) refers to ff.o(i.ld_qword) for ld_qword
    ff.o(i.f_opendir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_opendir) refers to diskio.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_printf) refers to ff.o(i.putc_bfd) for putc_bfd
    ff.o(i.f_printf) refers to ff.o(i.f_write) for f_write
    ff.o(i.f_putc) refers to ff.o(i.putc_bfd) for putc_bfd
    ff.o(i.f_putc) refers to ff.o(i.f_write) for f_write
    ff.o(i.f_puts) refers to ff.o(i.putc_bfd) for putc_bfd
    ff.o(i.f_puts) refers to ff.o(i.f_write) for f_write
    ff.o(i.f_read) refers to ff.o(i.validate) for validate
    ff.o(i.f_read) refers to ff.o(i.clmt_clust) for clmt_clust
    ff.o(i.f_read) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_read) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_read) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_read) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_read) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_readdir) refers to ff.o(i.validate) for validate
    ff.o(i.f_readdir) refers to diskio.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_readdir) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_readdir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_readdir) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_readdir) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.f_readdir) refers to diskio.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_rename) refers to ff.o(i.get_ldnumber) for get_ldnumber
    ff.o(i.f_rename) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_rename) refers to diskio.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_rename) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_rename) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_rename) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_rename) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.f_rename) refers to ff.o(i.st_word) for st_word
    ff.o(i.f_rename) refers to ff.o(i.store_xdir) for store_xdir
    ff.o(i.f_rename) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.f_rename) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_rename) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_rename) refers to ff.o(i.st_clust) for st_clust
    ff.o(i.f_rename) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_rename) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_rename) refers to diskio.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_setlabel) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_setlabel) refers to mycc936.o(i.ff_convert) for ff_convert
    ff.o(i.f_setlabel) refers to ff.o(i.chk_chr) for chk_chr
    ff.o(i.f_setlabel) refers to ff.o(i.st_word) for st_word
    ff.o(i.f_setlabel) refers to mycc936.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.f_setlabel) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_setlabel) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_setlabel) refers to ff.o(i.dir_alloc) for dir_alloc
    ff.o(i.f_setlabel) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.f_setlabel) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_setlabel) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_setlabel) refers to ff.o(.constdata) for .constdata
    ff.o(i.f_stat) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_stat) refers to diskio.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_stat) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_stat) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_stat) refers to diskio.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_sync) refers to ff.o(i.validate) for validate
    ff.o(i.f_sync) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_sync) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_sync) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_sync) refers to ff.o(i.fill_fat_chain) for fill_fat_chain
    ff.o(i.f_sync) refers to diskio.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_sync) refers to ff.o(i.load_obj_dir) for load_obj_dir
    ff.o(i.f_sync) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.f_sync) refers to ff.o(i.st_qword) for st_qword
    ff.o(i.f_sync) refers to ff.o(i.store_xdir) for store_xdir
    ff.o(i.f_sync) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_sync) refers to diskio.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_sync) refers to ff.o(i.st_clust) for st_clust
    ff.o(i.f_sync) refers to ff.o(i.st_word) for st_word
    ff.o(i.f_truncate) refers to ff.o(i.validate) for validate
    ff.o(i.f_truncate) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_truncate) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_truncate) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_unlink) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_unlink) refers to diskio.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_unlink) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_unlink) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.f_unlink) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.f_unlink) refers to ff.o(i.ld_qword) for ld_qword
    ff.o(i.f_unlink) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_unlink) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_unlink) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_unlink) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_unlink) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_unlink) refers to diskio.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_write) refers to ff.o(i.validate) for validate
    ff.o(i.f_write) refers to ff.o(i.clmt_clust) for clmt_clust
    ff.o(i.f_write) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_write) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_write) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_write) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_write) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.fill_fat_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.find_bitmap) refers to ff.o(i.move_window) for move_window
    ff.o(i.find_volume) refers to ff.o(i.get_ldnumber) for get_ldnumber
    ff.o(i.find_volume) refers to diskio.o(i.disk_status) for disk_status
    ff.o(i.find_volume) refers to diskio.o(i.disk_initialize) for disk_initialize
    ff.o(i.find_volume) refers to ff.o(i.check_fs) for check_fs
    ff.o(i.find_volume) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.find_volume) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.find_volume) refers to ff.o(i.ld_qword) for ld_qword
    ff.o(i.find_volume) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.find_volume) refers to ff.o(i.move_window) for move_window
    ff.o(i.find_volume) refers to ff.o(.data) for .data
    ff.o(i.follow_path) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.follow_path) refers to ff.o(i.create_name) for create_name
    ff.o(i.follow_path) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.follow_path) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.follow_path) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.follow_path) refers to ff.o(i.ld_qword) for ld_qword
    ff.o(i.gen_numname) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.get_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.get_fat) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.get_fat) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.get_fileinfo) refers to ff.o(i.get_xdir_info) for get_xdir_info
    ff.o(i.get_fileinfo) refers to mycc936.o(i.ff_convert) for ff_convert
    ff.o(i.get_fileinfo) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.get_fileinfo) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.get_xdir_info) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.get_xdir_info) refers to mycc936.o(i.ff_convert) for ff_convert
    ff.o(i.get_xdir_info) refers to ff.o(i.ld_qword) for ld_qword
    ff.o(i.ld_clust) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.load_obj_dir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.load_obj_dir) refers to ff.o(i.load_xdir) for load_xdir
    ff.o(i.load_xdir) refers to ff.o(i.move_window) for move_window
    ff.o(i.load_xdir) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.load_xdir) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.load_xdir) refers to ff.o(i.xdir_sum) for xdir_sum
    ff.o(i.load_xdir) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.move_window) refers to ff.o(i.sync_window) for sync_window
    ff.o(i.move_window) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.pick_lfn) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.pick_lfn) refers to ff.o(.constdata) for .constdata
    ff.o(i.put_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.put_fat) refers to ff.o(i.st_word) for st_word
    ff.o(i.put_fat) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.put_fat) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.putc_bfd) refers to ff.o(i.f_write) for f_write
    ff.o(i.remove_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.remove_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.remove_chain) refers to ff.o(i.change_bitmap) for change_bitmap
    ff.o(i.st_clust) refers to ff.o(i.st_word) for st_word
    ff.o(i.store_xdir) refers to ff.o(i.xdir_sum) for xdir_sum
    ff.o(i.store_xdir) refers to ff.o(i.st_word) for st_word
    ff.o(i.store_xdir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.store_xdir) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.store_xdir) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.store_xdir) refers to ff.o(i.move_window) for move_window
    ff.o(i.sync_fs) refers to ff.o(i.sync_window) for sync_window
    ff.o(i.sync_fs) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.sync_fs) refers to ff.o(i.st_word) for st_word
    ff.o(i.sync_fs) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.sync_fs) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.sync_fs) refers to diskio.o(i.disk_ioctl) for disk_ioctl
    ff.o(i.sync_window) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.validate) refers to diskio.o(i.disk_status) for disk_status
    ff.o(i.xname_sum) refers to mycc936.o(i.ff_wtoupper) for ff_wtoupper
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.SD_Init) for SD_Init
    diskio.o(i.disk_initialize) refers to w25qxx.o(i.W25QXX_Init) for W25QXX_Init
    diskio.o(i.disk_ioctl) refers to sdio_sdcard.o(.bss) for SDCardInfo
    diskio.o(i.disk_ioctl) refers to diskio.o(.data) for .data
    diskio.o(i.disk_read) refers to sdio_sdcard.o(i.SD_Init) for SD_Init
    diskio.o(i.disk_read) refers to sdio_sdcard.o(i.SD_ReadDisk) for SD_ReadDisk
    diskio.o(i.disk_read) refers to w25qxx.o(i.W25QXX_Read) for W25QXX_Read
    diskio.o(i.disk_write) refers to sdio_sdcard.o(i.SD_Init) for SD_Init
    diskio.o(i.disk_write) refers to sdio_sdcard.o(i.SD_WriteDisk) for SD_WriteDisk
    diskio.o(i.disk_write) refers to w25qxx.o(i.W25QXX_Write) for W25QXX_Write
    diskio.o(i.ff_memalloc) refers to malloc.o(i.mymalloc) for mymalloc
    diskio.o(i.ff_memfree) refers to malloc.o(i.myfree) for myfree
    exfuns.o(i.exf_copy) refers to malloc.o(i.mymalloc) for mymalloc
    exfuns.o(i.exf_copy) refers to ff.o(i.f_open) for f_open
    exfuns.o(i.exf_copy) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    exfuns.o(i.exf_copy) refers to ff.o(i.f_write) for f_write
    exfuns.o(i.exf_copy) refers to ff.o(i.f_read) for f_read
    exfuns.o(i.exf_copy) refers to ff.o(i.f_close) for f_close
    exfuns.o(i.exf_copy) refers to malloc.o(i.myfree) for myfree
    exfuns.o(i.exf_fdcopy) refers to malloc.o(i.mymalloc) for mymalloc
    exfuns.o(i.exf_fdcopy) refers to strcat.o(.text) for strcat
    exfuns.o(i.exf_fdcopy) refers to ff.o(i.f_opendir) for f_opendir
    exfuns.o(i.exf_fdcopy) refers to exfuns.o(i.exf_get_src_dname) for exf_get_src_dname
    exfuns.o(i.exf_fdcopy) refers to ff.o(i.f_mkdir) for f_mkdir
    exfuns.o(i.exf_fdcopy) refers to strlen.o(.text) for strlen
    exfuns.o(i.exf_fdcopy) refers to ff.o(i.f_readdir) for f_readdir
    exfuns.o(i.exf_fdcopy) refers to exfuns.o(i.exf_copy) for exf_copy
    exfuns.o(i.exf_fdcopy) refers to malloc.o(i.myfree) for myfree
    exfuns.o(i.exf_fdsize) refers to malloc.o(i.mymalloc) for mymalloc
    exfuns.o(i.exf_fdsize) refers to strcat.o(.text) for strcat
    exfuns.o(i.exf_fdsize) refers to ff.o(i.f_opendir) for f_opendir
    exfuns.o(i.exf_fdsize) refers to strlen.o(.text) for strlen
    exfuns.o(i.exf_fdsize) refers to ff.o(i.f_readdir) for f_readdir
    exfuns.o(i.exf_fdsize) refers to malloc.o(i.myfree) for myfree
    exfuns.o(i.exf_getfree) refers to ff.o(i.f_getfree) for f_getfree
    exfuns.o(i.exfuns_init) refers to malloc.o(i.mymalloc) for mymalloc
    exfuns.o(i.exfuns_init) refers to exfuns.o(.data) for .data
    exfuns.o(i.f_typetell) refers to strcpy.o(.text) for strcpy
    exfuns.o(i.f_typetell) refers to exfuns.o(i.char_upper) for char_upper
    exfuns.o(i.f_typetell) refers to strcmpv7m.o(.text) for strcmp
    exfuns.o(i.f_typetell) refers to exfuns.o(.constdata) for .constdata
    exfuns.o(.constdata) refers to exfuns.o(.conststring) for .conststring
    fattester.o(i.mf_close) refers to ff.o(i.f_close) for f_close
    fattester.o(i.mf_close) refers to exfuns.o(.data) for file
    fattester.o(i.mf_closedir) refers to ff.o(i.f_closedir) for f_closedir
    fattester.o(i.mf_closedir) refers to exfuns.o(.bss) for dir
    fattester.o(i.mf_fmkfs) refers to ff.o(i.f_mkfs) for f_mkfs
    fattester.o(i.mf_getlabel) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fattester.o(i.mf_getlabel) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    fattester.o(i.mf_getlabel) refers to _printf_str.o(.text) for _printf_str
    fattester.o(i.mf_getlabel) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    fattester.o(i.mf_getlabel) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    fattester.o(i.mf_getlabel) refers to ff.o(i.f_getlabel) for f_getlabel
    fattester.o(i.mf_getlabel) refers to noretval__2printf.o(.text) for __2printf
    fattester.o(i.mf_gets) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fattester.o(i.mf_gets) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    fattester.o(i.mf_gets) refers to _printf_str.o(.text) for _printf_str
    fattester.o(i.mf_gets) refers to ff.o(i.f_gets) for f_gets
    fattester.o(i.mf_gets) refers to noretval__2printf.o(.text) for __2printf
    fattester.o(i.mf_gets) refers to exfuns.o(.data) for file
    fattester.o(i.mf_lseek) refers to ff.o(i.f_lseek) for f_lseek
    fattester.o(i.mf_lseek) refers to exfuns.o(.data) for file
    fattester.o(i.mf_mkdir) refers to ff.o(i.f_mkdir) for f_mkdir
    fattester.o(i.mf_mount) refers to ff.o(i.f_mount) for f_mount
    fattester.o(i.mf_mount) refers to exfuns.o(.data) for fs
    fattester.o(i.mf_open) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fattester.o(i.mf_open) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    fattester.o(i.mf_open) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    fattester.o(i.mf_open) refers to noretval__2printf.o(.text) for __2printf
    fattester.o(i.mf_open) refers to ff.o(i.f_open) for f_open
    fattester.o(i.mf_open) refers to exfuns.o(.data) for file
    fattester.o(i.mf_opendir) refers to ff.o(i.f_opendir) for f_opendir
    fattester.o(i.mf_opendir) refers to exfuns.o(.bss) for dir
    fattester.o(i.mf_putc) refers to ff.o(i.f_putc) for f_putc
    fattester.o(i.mf_putc) refers to exfuns.o(.data) for file
    fattester.o(i.mf_puts) refers to ff.o(i.f_puts) for f_puts
    fattester.o(i.mf_puts) refers to exfuns.o(.data) for file
    fattester.o(i.mf_read) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fattester.o(i.mf_read) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    fattester.o(i.mf_read) refers to _printf_dec.o(.text) for _printf_int_dec
    fattester.o(i.mf_read) refers to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    fattester.o(i.mf_read) refers to _printf_str.o(.text) for _printf_str
    fattester.o(i.mf_read) refers to noretval__2printf.o(.text) for __2printf
    fattester.o(i.mf_read) refers to ff.o(i.f_read) for f_read
    fattester.o(i.mf_read) refers to exfuns.o(.data) for fatbuf
    fattester.o(i.mf_read) refers to exfuns.o(.data) for br
    fattester.o(i.mf_readdir) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fattester.o(i.mf_readdir) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    fattester.o(i.mf_readdir) refers to _printf_dec.o(.text) for _printf_int_dec
    fattester.o(i.mf_readdir) refers to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    fattester.o(i.mf_readdir) refers to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    fattester.o(i.mf_readdir) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    fattester.o(i.mf_readdir) refers to _printf_str.o(.text) for _printf_str
    fattester.o(i.mf_readdir) refers to ff.o(i.f_readdir) for f_readdir
    fattester.o(i.mf_readdir) refers to noretval__2printf.o(.text) for __2printf
    fattester.o(i.mf_readdir) refers to exfuns.o(.bss) for fileinfo
    fattester.o(i.mf_readdir) refers to exfuns.o(.bss) for dir
    fattester.o(i.mf_rename) refers to ff.o(i.f_rename) for f_rename
    fattester.o(i.mf_scan_files) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fattester.o(i.mf_scan_files) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    fattester.o(i.mf_scan_files) refers to _printf_str.o(.text) for _printf_str
    fattester.o(i.mf_scan_files) refers to ff.o(i.f_opendir) for f_opendir
    fattester.o(i.mf_scan_files) refers to noretval__2printf.o(.text) for __2printf
    fattester.o(i.mf_scan_files) refers to ff.o(i.f_readdir) for f_readdir
    fattester.o(i.mf_scan_files) refers to exfuns.o(.bss) for dir
    fattester.o(i.mf_scan_files) refers to exfuns.o(.bss) for fileinfo
    fattester.o(i.mf_setlabel) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fattester.o(i.mf_setlabel) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    fattester.o(i.mf_setlabel) refers to _printf_str.o(.text) for _printf_str
    fattester.o(i.mf_setlabel) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    fattester.o(i.mf_setlabel) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    fattester.o(i.mf_setlabel) refers to ff.o(i.f_setlabel) for f_setlabel
    fattester.o(i.mf_setlabel) refers to noretval__2printf.o(.text) for __2printf
    fattester.o(i.mf_showfree) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fattester.o(i.mf_showfree) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    fattester.o(i.mf_showfree) refers to _printf_dec.o(.text) for _printf_int_dec
    fattester.o(i.mf_showfree) refers to ff.o(i.f_getfree) for f_getfree
    fattester.o(i.mf_showfree) refers to noretval__2printf.o(.text) for __2printf
    fattester.o(i.mf_size) refers to exfuns.o(.data) for file
    fattester.o(i.mf_tell) refers to exfuns.o(.data) for file
    fattester.o(i.mf_unlink) refers to ff.o(i.f_unlink) for f_unlink
    fattester.o(i.mf_write) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fattester.o(i.mf_write) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    fattester.o(i.mf_write) refers to _printf_dec.o(.text) for _printf_int_dec
    fattester.o(i.mf_write) refers to noretval__2printf.o(.text) for __2printf
    fattester.o(i.mf_write) refers to ff.o(i.f_write) for f_write
    fattester.o(i.mf_write) refers to exfuns.o(.data) for bw
    fattester.o(i.mf_write) refers to exfuns.o(.data) for file
    mycc936.o(i.ff_convert) refers to w25qxx.o(i.W25QXX_Read) for W25QXX_Read
    mycc936.o(i.ff_convert) refers to fontupd.o(.bss) for ftinfo
    mycc936.o(i.ff_wtoupper) refers to mycc936.o(.constdata) for .constdata
    text.o(i.Get_HzMat) refers to w25qxx.o(i.W25QXX_Read) for W25QXX_Read
    text.o(i.Get_HzMat) refers to fontupd.o(.bss) for ftinfo
    text.o(i.Show_Font) refers to text.o(i.Get_HzMat) for Get_HzMat
    text.o(i.Show_Font) refers to lcd.o(i.LCD_Fast_DrawPoint) for LCD_Fast_DrawPoint
    text.o(i.Show_Font) refers to lcd.o(.data) for POINT_COLOR
    text.o(i.Show_Str) refers to text.o(i.Show_Font) for Show_Font
    text.o(i.Show_Str) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    text.o(i.Show_Str_Mid) refers to strlen.o(.text) for strlen
    text.o(i.Show_Str_Mid) refers to text.o(i.Show_Str) for Show_Str
    text.o(i.Show_Str_Mid) refers to lcd.o(.bss) for lcddev
    fontupd.o(i.font_init) refers to w25qxx.o(i.W25QXX_Init) for W25QXX_Init
    fontupd.o(i.font_init) refers to w25qxx.o(i.W25QXX_Read) for W25QXX_Read
    fontupd.o(i.font_init) refers to delay.o(i.delay_ms) for delay_ms
    fontupd.o(i.font_init) refers to fontupd.o(.bss) for .bss
    fontupd.o(i.fupd_prog) refers to lcd.o(i.LCD_ShowString) for LCD_ShowString
    fontupd.o(i.fupd_prog) refers to lcd.o(i.LCD_ShowNum) for LCD_ShowNum
    fontupd.o(i.updata_fontx) refers to malloc.o(i.mymalloc) for mymalloc
    fontupd.o(i.updata_fontx) refers to ff.o(i.f_open) for f_open
    fontupd.o(i.updata_fontx) refers to ff.o(i.f_close) for f_close
    fontupd.o(i.updata_fontx) refers to malloc.o(i.myfree) for myfree
    fontupd.o(i.updata_fontx) refers to ff.o(i.f_read) for f_read
    fontupd.o(i.updata_fontx) refers to w25qxx.o(i.W25QXX_Write) for W25QXX_Write
    fontupd.o(i.updata_fontx) refers to fontupd.o(i.fupd_prog) for fupd_prog
    fontupd.o(i.updata_fontx) refers to fontupd.o(.bss) for .bss
    fontupd.o(i.update_font) refers to malloc.o(i.mymalloc) for mymalloc
    fontupd.o(i.update_font) refers to strcpy.o(.text) for strcpy
    fontupd.o(i.update_font) refers to strcat.o(.text) for strcat
    fontupd.o(i.update_font) refers to ff.o(i.f_open) for f_open
    fontupd.o(i.update_font) refers to malloc.o(i.myfree) for myfree
    fontupd.o(i.update_font) refers to lcd.o(i.LCD_ShowString) for LCD_ShowString
    fontupd.o(i.update_font) refers to fontupd.o(i.fupd_prog) for fupd_prog
    fontupd.o(i.update_font) refers to w25qxx.o(i.W25QXX_Read) for W25QXX_Read
    fontupd.o(i.update_font) refers to w25qxx.o(i.W25QXX_Erase_Sector) for W25QXX_Erase_Sector
    fontupd.o(i.update_font) refers to fontupd.o(i.updata_fontx) for updata_fontx
    fontupd.o(i.update_font) refers to w25qxx.o(i.W25QXX_Write) for W25QXX_Write
    fontupd.o(i.update_font) refers to fontupd.o(.bss) for .bss
    fontupd.o(i.update_font) refers to fontupd.o(.constdata) for .constdata
    fontupd.o(.constdata) refers to fontupd.o(.conststring) for .conststring
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(i.fputc) for fputc
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (120 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (52 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (28 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (28 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUID), (28 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (292 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (56 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (76 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (72 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (24 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (232 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (140 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (80 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (92 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (340 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (108 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (128 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (142 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (92 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (58 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (84 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (172 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (102 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (94 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (88 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (40 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (186 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit), (186 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (128 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAError), (74 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_EndTxTransfer), (18 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout), (98 bytes).
    Removing stm32f4xx_hal_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_usart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Abort), (100 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Abort_IT), (168 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_DMAPause), (38 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_DMAResume), (38 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_DMAStop), (92 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_DeInit), (46 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_GetError), (4 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_GetState), (6 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler), (320 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Init), (84 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Receive), (230 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Receive_DMA), (200 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Receive_IT), (80 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Transmit), (186 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive), (274 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA), (220 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_IT), (102 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_DMA), (128 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_IT), (62 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMAAbortOnError), (16 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMAError), (80 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMAReceiveCplt), (100 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMARxAbortCallback), (38 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMARxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMATransmitCplt), (62 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMATxAbortCallback), (38 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_EndRxTransfer), (28 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_EndTxTransfer), (18 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_Receive_IT), (176 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_SetConfig), (260 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_TransmitReceive_IT), (232 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_Transmit_IT), (98 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout), (114 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift), (40 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam), (84 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_SetConfig), (40 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Abort), (142 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (86 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler), (424 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Init), (212 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (284 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (80 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT), (120 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_dma.o(.constdata), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (30 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2452 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (44 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource), (240 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (174 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (260 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (260 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (150 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (50 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (272 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (68 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (116 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (116 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (284 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (54 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (44 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (100 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler), (358 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (54 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (240 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (88 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (136 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (218 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (74 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (60 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (80 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (100 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (208 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init), (54 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (240 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (136 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (68 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchronization), (66 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchronization_IT), (66 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig), (20 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig), (88 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig), (100 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig), (80 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage), (36 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutationCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (90 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutationEvent), (92 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutationEvent_DMA), (124 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutationEvent_IT), (102 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (56 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (176 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (28 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (38 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (46 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (56 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (56 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization), (80 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (36 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (96 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (124 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (134 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (46 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (88 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (36 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (96 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (124 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (134 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (22 bytes).
    Removing stm32f4xx_hal_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Abort), (192 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT), (232 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAPause), (38 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAResume), (38 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAStop), (40 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit), (46 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_GetError), (4 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_GetState), (6 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler), (212 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_MspInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive), (490 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA), (244 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT), (196 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit), (404 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA), (292 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT), (196 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA), (212 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT), (180 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT), (64 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BITCRC), (34 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT), (64 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BITCRC), (44 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT), (74 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT), (72 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR), (80 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR), (22 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR), (176 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR), (104 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR), (124 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError), (16 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAError), (34 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt), (160 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback), (70 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt), (90 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt), (152 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback), (104 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT), (72 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_16BITCRC), (34 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT), (72 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_8BITCRC), (26 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT), (50 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT), (48 bytes).
    Removing stm32f4xx_hal_sram.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit), (28 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_GetState), (6 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_16b), (54 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_32b), (50 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_8b), (54 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA), (60 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable), (48 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable), (42 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_16b), (66 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_32b), (62 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_8b), (66 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA), (80 bytes).
    Removing stm32f4xx_ll_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ll_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ll_fsmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_AttributeSpace_Timing_Init), (48 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_CommonSpace_Timing_Init), (48 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_DeInit), (52 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_ECC_Disable), (26 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_ECC_Enable), (26 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_GetECC), (78 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_Init), (80 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_DeInit), (48 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_WriteOperation_Disable), (16 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_WriteOperation_Enable), (16 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_AttributeSpace_Timing_Init), (28 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_CommonSpace_Timing_Init), (28 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_DeInit), (30 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_IOSpace_Timing_Init), (28 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_Init), (40 bytes).
    Removing stm32f4xx_hal_sd.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_sd.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_sd.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_Abort), (94 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_AbortCallback), (2 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_Abort_IT), (140 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_DeInit), (38 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_Erase), (184 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_GetCardCID), (178 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_GetCardStatus), (182 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_GetError), (4 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_GetState), (6 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler), (448 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_MspInit), (2 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA), (248 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_IT), (194 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA), (248 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_IT), (200 bytes).
    Removing stm32f4xx_hal_sd.o(i.SD_DMAError), (86 bytes).
    Removing stm32f4xx_hal_sd.o(i.SD_DMAReceiveCplt), (62 bytes).
    Removing stm32f4xx_hal_sd.o(i.SD_DMARxAbort), (72 bytes).
    Removing stm32f4xx_hal_sd.o(i.SD_DMATransmitCplt), (14 bytes).
    Removing stm32f4xx_hal_sd.o(i.SD_DMATxAbort), (72 bytes).
    Removing stm32f4xx_hal_sd.o(i.SD_SendSDStatus), (236 bytes).
    Removing stm32f4xx_ll_sdmmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ll_sdmmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ll_sdmmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDIO_GetCommandResponse), (6 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDIO_GetDataCounter), (4 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDIO_GetFIFOCount), (6 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDIO_PowerState_OFF), (8 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDIO_SetSDMMCReadWaitMode), (14 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdErase), (48 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdEraseEndAdd), (48 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdEraseStartAdd), (48 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOpCondition), (42 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseEndAdd), (48 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseStartAdd), (48 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStatusRegister), (48 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSwitch), (48 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.rrx_text), (6 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.rrx_text), (6 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(.rrx_text), (6 bytes).
    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing lcd.o(.rrx_text), (6 bytes).
    Removing lcd.o(i.LCD_BGR2RGB), (18 bytes).
    Removing lcd.o(i.LCD_Color_Fill), (86 bytes).
    Removing lcd.o(i.LCD_DisplayOff), (28 bytes).
    Removing lcd.o(i.LCD_DisplayOn), (28 bytes).
    Removing lcd.o(i.LCD_DrawLine), (150 bytes).
    Removing lcd.o(i.LCD_DrawPoint), (28 bytes).
    Removing lcd.o(i.LCD_DrawRectangle), (56 bytes).
    Removing lcd.o(i.LCD_Draw_Circle), (176 bytes).
    Removing lcd.o(i.LCD_ReadPoint), (132 bytes).
    Removing lcd.o(i.LCD_ReadReg), (20 bytes).
    Removing lcd.o(i.LCD_Set_Window), (340 bytes).
    Removing lcd.o(i.LCD_ShowxNum), (134 bytes).
    Removing lcd.o(i.LCD_WriteRAM), (10 bytes).
    Removing lcd.o(i.opt_delay), (8 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing sram.o(.rev16_text), (4 bytes).
    Removing sram.o(.revsh_text), (4 bytes).
    Removing sram.o(.rrx_text), (6 bytes).
    Removing sram.o(i.FSMC_SRAM_ReadBuffer), (22 bytes).
    Removing sram.o(i.FSMC_SRAM_WriteBuffer), (24 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing w25qxx.o(.rev16_text), (4 bytes).
    Removing w25qxx.o(.revsh_text), (4 bytes).
    Removing w25qxx.o(.rrx_text), (6 bytes).
    Removing w25qxx.o(i.W25QXX_PowerDown), (40 bytes).
    Removing w25qxx.o(i.W25QXX_WAKEUP), (40 bytes).
    Removing w25qxx.o(i.W25QXX_Write_Disable), (32 bytes).
    Removing w25qxx.o(i.W25QXX_Write_SR), (60 bytes).
    Removing sdio_sdcard.o(.rev16_text), (4 bytes).
    Removing sdio_sdcard.o(.revsh_text), (4 bytes).
    Removing sdio_sdcard.o(.rrx_text), (6 bytes).
    Removing sdio_sdcard.o(i.SD_GetCardInfo), (12 bytes).
    Removing sdio_sdcard.o(.bss), (96 bytes).
    Removing sdio_sdcard.o(.bss), (96 bytes).
    Removing sdio_sdcard.o(.bss), (512 bytes).
    Removing usmart.o(.rev16_text), (4 bytes).
    Removing usmart.o(.revsh_text), (4 bytes).
    Removing usmart.o(.rrx_text), (6 bytes).
    Removing usmart_str.o(.rev16_text), (4 bytes).
    Removing usmart_str.o(.revsh_text), (4 bytes).
    Removing usmart_str.o(.rrx_text), (6 bytes).
    Removing usmart_config.o(.rev16_text), (4 bytes).
    Removing usmart_config.o(.revsh_text), (4 bytes).
    Removing usmart_config.o(.rrx_text), (6 bytes).
    Removing malloc.o(.rev16_text), (4 bytes).
    Removing malloc.o(.revsh_text), (4 bytes).
    Removing malloc.o(.rrx_text), (6 bytes).
    Removing malloc.o(i.mymemcpy), (16 bytes).
    Removing malloc.o(i.myrealloc), (60 bytes).
    Removing ff.o(i.f_printf), (484 bytes).
    Removing ff.o(i.f_stat), (98 bytes).
    Removing ff.o(i.f_truncate), (188 bytes).
    Removing diskio.o(.rev16_text), (4 bytes).
    Removing diskio.o(.revsh_text), (4 bytes).
    Removing diskio.o(.rrx_text), (6 bytes).
    Removing exfuns.o(.rev16_text), (4 bytes).
    Removing exfuns.o(.revsh_text), (4 bytes).
    Removing exfuns.o(.rrx_text), (6 bytes).
    Removing exfuns.o(i.char_upper), (14 bytes).
    Removing exfuns.o(i.exf_copy), (350 bytes).
    Removing exfuns.o(i.exf_fdcopy), (440 bytes).
    Removing exfuns.o(i.exf_fdsize), (204 bytes).
    Removing exfuns.o(i.exf_get_src_dname), (40 bytes).
    Removing exfuns.o(i.exf_getfree), (46 bytes).
    Removing exfuns.o(i.f_typetell), (136 bytes).
    Removing exfuns.o(.constdata), (112 bytes).
    Removing exfuns.o(.conststring), (72 bytes).
    Removing fattester.o(.rev16_text), (4 bytes).
    Removing fattester.o(.revsh_text), (4 bytes).
    Removing fattester.o(.rrx_text), (6 bytes).
    Removing mycc936.o(.rev16_text), (4 bytes).
    Removing mycc936.o(.revsh_text), (4 bytes).
    Removing mycc936.o(.rrx_text), (6 bytes).
    Removing text.o(.rev16_text), (4 bytes).
    Removing text.o(.revsh_text), (4 bytes).
    Removing text.o(.rrx_text), (6 bytes).
    Removing text.o(i.Show_Str_Mid), (88 bytes).
    Removing fontupd.o(.rev16_text), (4 bytes).
    Removing fontupd.o(.revsh_text), (4 bytes).
    Removing fontupd.o(.rrx_text), (6 bytes).

602 unused section(s) (total 38484 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcat.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ..\CORE\startup_stm32f407xx.s            0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    ..\FATFS\exfuns\exfuns.c                 0x00000000   Number         0  exfuns.o ABSOLUTE
    ..\FATFS\exfuns\fattester.c              0x00000000   Number         0  fattester.o ABSOLUTE
    ..\FATFS\exfuns\mycc936.c                0x00000000   Number         0  mycc936.o ABSOLUTE
    ..\FATFS\src\diskio.c                    0x00000000   Number         0  diskio.o ABSOLUTE
    ..\FATFS\src\ff.c                        0x00000000   Number         0  ff.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_sd.c 0x00000000   Number         0  stm32f4xx_hal_sd.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_spi.c 0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_usart.c 0x00000000   Number         0  stm32f4xx_hal_usart.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_ll_fsmc.c 0x00000000   Number         0  stm32f4xx_ll_fsmc.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_ll_sdmmc.c 0x00000000   Number         0  stm32f4xx_ll_sdmmc.o ABSOLUTE
    ..\HARDWARE\KEY\key.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HARDWARE\LCD\lcd.c                    0x00000000   Number         0  lcd.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\SDIO\sdio_sdcard.c           0x00000000   Number         0  sdio_sdcard.o ABSOLUTE
    ..\HARDWARE\SPI\spi.c                    0x00000000   Number         0  spi.o ABSOLUTE
    ..\HARDWARE\SRAM\sram.c                  0x00000000   Number         0  sram.o ABSOLUTE
    ..\HARDWARE\W25QXX\w25qxx.c              0x00000000   Number         0  w25qxx.o ABSOLUTE
    ..\MALLOC\malloc.c                       0x00000000   Number         0  malloc.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\TEXT\fontupd.c                        0x00000000   Number         0  fontupd.o ABSOLUTE
    ..\TEXT\text.c                           0x00000000   Number         0  text.o ABSOLUTE
    ..\USMART\usmart.c                       0x00000000   Number         0  usmart.o ABSOLUTE
    ..\USMART\usmart_config.c                0x00000000   Number         0  usmart_config.o ABSOLUTE
    ..\USMART\usmart_str.c                   0x00000000   Number         0  usmart_str.o ABSOLUTE
    ..\\FATFS\\exfuns\\exfuns.c              0x00000000   Number         0  exfuns.o ABSOLUTE
    ..\\FATFS\\exfuns\\fattester.c           0x00000000   Number         0  fattester.o ABSOLUTE
    ..\\FATFS\\exfuns\\mycc936.c             0x00000000   Number         0  mycc936.o ABSOLUTE
    ..\\FATFS\\src\\diskio.c                 0x00000000   Number         0  diskio.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_sd.c 0x00000000   Number         0  stm32f4xx_hal_sd.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_spi.c 0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_usart.c 0x00000000   Number         0  stm32f4xx_hal_usart.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_ll_fsmc.c 0x00000000   Number         0  stm32f4xx_ll_fsmc.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_ll_sdmmc.c 0x00000000   Number         0  stm32f4xx_ll_sdmmc.o ABSOLUTE
    ..\\HARDWARE\\KEY\\key.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\\HARDWARE\\LCD\\lcd.c                 0x00000000   Number         0  lcd.o ABSOLUTE
    ..\\HARDWARE\\LED\\led.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\\HARDWARE\\SDIO\\sdio_sdcard.c        0x00000000   Number         0  sdio_sdcard.o ABSOLUTE
    ..\\HARDWARE\\SPI\\spi.c                 0x00000000   Number         0  spi.o ABSOLUTE
    ..\\HARDWARE\\SRAM\\sram.c               0x00000000   Number         0  sram.o ABSOLUTE
    ..\\HARDWARE\\W25QXX\\w25qxx.c           0x00000000   Number         0  w25qxx.o ABSOLUTE
    ..\\MALLOC\\malloc.c                     0x00000000   Number         0  malloc.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    ..\\TEXT\\fontupd.c                      0x00000000   Number         0  fontupd.o ABSOLUTE
    ..\\TEXT\\text.c                         0x00000000   Number         0  text.o ABSOLUTE
    ..\\USMART\\usmart.c                     0x00000000   Number         0  usmart.o ABSOLUTE
    ..\\USMART\\usmart_config.c              0x00000000   Number         0  usmart_config.o ABSOLUTE
    ..\\USMART\\usmart_str.c                 0x00000000   Number         0  usmart_str.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f4xx_hal_msp.c                      0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    stm32f4xx_hal_msp.c                      0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x080001c4   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000220   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x0800023c   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000007  0x0800023c   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000009  0x08000246   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x0800024c   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000252   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000E  0x08000258   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$00000013  0x0800025e   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x08000264   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x0800026a   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0800026e   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x08000270   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000274   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000274   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000274   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000274   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000274   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000274   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000274   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000274   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000274   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000274   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000274   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000274   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000274   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000274   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000274   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000274   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000274   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000274   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000274   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000274   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000276   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000278   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000278   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000278   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000278   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000278   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000278   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000278   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x0800027a   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0800027a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0800027a   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000280   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000280   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000284   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000284   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800028c   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800028e   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800028e   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000292   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x08000298   Section       16  sys.o(.emb_text)
    $v0                                      0x08000298   Number         0  sys.o(.emb_text)
    .text                                    0x080002a8   Section       64  startup_stm32f407xx.o(.text)
    $v0                                      0x080002a8   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x080002e8   Section        2  use_no_semi_2.o(.text)
    .text                                    0x080002ea   Section      238  lludivv7m.o(.text)
    .text                                    0x080003d8   Section        0  noretval__2printf.o(.text)
    .text                                    0x080003f0   Section        0  _printf_pad.o(.text)
    .text                                    0x0800043e   Section        0  _printf_str.o(.text)
    .text                                    0x08000490   Section        0  _printf_dec.o(.text)
    .text                                    0x08000508   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x08000584   Section        0  _printf_hex_int.o(.text)
    .text                                    0x080005dc   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000764   Section        0  strcpy.o(.text)
    .text                                    0x080007ac   Section        0  strcat.o(.text)
    .text                                    0x080007c4   Section        0  heapauxi.o(.text)
    .text                                    0x080007ca   Section        2  use_no_semi.o(.text)
    .text                                    0x080007cc   Section      138  lludiv10.o(.text)
    .text                                    0x08000856   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000908   Section        0  _printf_char.o(.text)
    .text                                    0x08000934   Section        0  _printf_char_file.o(.text)
    .text                                    0x08000958   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000959   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000988   Section        0  ferror.o(.text)
    .text                                    0x08000990   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080009da   Section        0  exit.o(.text)
    .text                                    0x080009ec   Section        8  libspace.o(.text)
    i.BusFault_Handler                       0x080009f4   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x080009f6   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.FSMC_NORSRAM_Extended_Timing_Init      0x080009f8   Section        0  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Extended_Timing_Init)
    i.FSMC_NORSRAM_Init                      0x08000a38   Section        0  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Init)
    i.FSMC_NORSRAM_Timing_Init               0x08000a8c   Section        0  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Timing_Init)
    i.Get_HzMat                              0x08000ad0   Section        0  text.o(i.Get_HzMat)
    i.HAL_DMA_Abort_IT                       0x08000b5c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_Delay                              0x08000b80   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08000b9c   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08000d5c   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x08000d66   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetREVID                           0x08000d70   Section        0  stm32f4xx_hal.o(i.HAL_GetREVID)
    i.HAL_GetTick                            0x08000d7c   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08000d88   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08000d98   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08000dc4   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08000dec   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08000dee   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08000e04   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08000e44   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08000e68   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x08000fdc   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x0800100c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x0800103c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x080010ac   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SD_ConfigWideBusOperation          0x080013c0   Section        0  stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation)
    i.HAL_SD_GetCardCSD                      0x08001438   Section        0  stm32f4xx_hal_sd.o(i.HAL_SD_GetCardCSD)
    i.HAL_SD_GetCardInfo                     0x08001612   Section        0  stm32f4xx_hal_sd.o(i.HAL_SD_GetCardInfo)
    i.HAL_SD_GetCardState                    0x08001636   Section        0  stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState)
    i.HAL_SD_Init                            0x08001668   Section        0  stm32f4xx_hal_sd.o(i.HAL_SD_Init)
    i.HAL_SD_InitCard                        0x080016a0   Section        0  stm32f4xx_hal_sd.o(i.HAL_SD_InitCard)
    i.HAL_SD_MspInit                         0x080016f4   Section        0  sdio_sdcard.o(i.HAL_SD_MspInit)
    i.HAL_SD_ReadBlocks                      0x080017ac   Section        0  stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks)
    i.HAL_SD_WriteBlocks                     0x08001960   Section        0  stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks)
    i.HAL_SPI_Init                           0x08001ae6   Section        0  stm32f4xx_hal_spi.o(i.HAL_SPI_Init)
    i.HAL_SPI_MspInit                        0x08001b74   Section        0  spi.o(i.HAL_SPI_MspInit)
    i.HAL_SPI_TransmitReceive                0x08001bc4   Section        0  stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive)
    i.HAL_SRAM_Init                          0x08001e24   Section        0  stm32f4xx_hal_sram.o(i.HAL_SRAM_Init)
    i.HAL_SRAM_MspInit                       0x08001e7c   Section        0  lcd.o(i.HAL_SRAM_MspInit)
    i.HAL_SYSTICK_CLKSourceConfig            0x08001f34   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig)
    i.HAL_SYSTICK_Config                     0x08001f4c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIM_Base_Init                      0x08001f74   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08001faa   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x08001fac   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_UART_ErrorCallback                 0x08001fc4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_GetState                      0x08001fc6   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_GetState)
    i.HAL_UART_IRQHandler                    0x08001fd0   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x080020e0   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08002144   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x080021c0   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08002208   Section        0  usart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_TxCpltCallback                0x08002260   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08002264   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.KEY_Init                               0x08002280   Section        0  key.o(i.KEY_Init)
    i.KEY_Scan                               0x080022dc   Section        0  key.o(i.KEY_Scan)
    i.LCD_Clear                              0x0800235c   Section        0  lcd.o(i.LCD_Clear)
    i.LCD_Display_Dir                        0x08002390   Section        0  lcd.o(i.LCD_Display_Dir)
    i.LCD_Fast_DrawPoint                     0x08002478   Section        0  lcd.o(i.LCD_Fast_DrawPoint)
    i.LCD_Fill                               0x08002548   Section        0  lcd.o(i.LCD_Fill)
    i.LCD_Init                               0x08002590   Section        0  lcd.o(i.LCD_Init)
    i.LCD_Pow                                0x080050d8   Section        0  lcd.o(i.LCD_Pow)
    i.LCD_RD_DATA                            0x080050e8   Section        0  lcd.o(i.LCD_RD_DATA)
    i.LCD_SSD_BackLightSet                   0x080050fc   Section        0  lcd.o(i.LCD_SSD_BackLightSet)
    i.LCD_Scan_Dir                           0x0800514c   Section        0  lcd.o(i.LCD_Scan_Dir)
    i.LCD_SetCursor                          0x080052f4   Section        0  lcd.o(i.LCD_SetCursor)
    i.LCD_ShowChar                           0x080053e4   Section        0  lcd.o(i.LCD_ShowChar)
    i.LCD_ShowNum                            0x080054c0   Section        0  lcd.o(i.LCD_ShowNum)
    i.LCD_ShowString                         0x08005536   Section        0  lcd.o(i.LCD_ShowString)
    i.LCD_WR_DATA                            0x0800558a   Section        0  lcd.o(i.LCD_WR_DATA)
    i.LCD_WR_REG                             0x080055a2   Section        0  lcd.o(i.LCD_WR_REG)
    i.LCD_WriteRAM_Prepare                   0x080055bc   Section        0  lcd.o(i.LCD_WriteRAM_Prepare)
    i.LCD_WriteReg                           0x080055d0   Section        0  lcd.o(i.LCD_WriteReg)
    i.LED_Init                               0x080055dc   Section        0  led.o(i.LED_Init)
    i.MemManage_Handler                      0x0800562c   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x0800562e   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.NVIC_SetPriority                       0x08005630   Section        0  stm32f4xx_hal_cortex.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x08005631   Thumb Code    32  stm32f4xx_hal_cortex.o(i.NVIC_SetPriority)
    i.PendSV_Handler                         0x08005650   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.SDIO_ConfigData                        0x08005652   Section        0  stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData)
    i.SDIO_GetPowerState                     0x08005676   Section        0  stm32f4xx_ll_sdmmc.o(i.SDIO_GetPowerState)
    i.SDIO_GetResponse                       0x0800567e   Section        0  stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse)
    i.SDIO_Init                              0x0800568a   Section        0  stm32f4xx_ll_sdmmc.o(i.SDIO_Init)
    i.SDIO_PowerState_ON                     0x080056ae   Section        0  stm32f4xx_ll_sdmmc.o(i.SDIO_PowerState_ON)
    i.SDIO_ReadFIFO                          0x080056b6   Section        0  stm32f4xx_ll_sdmmc.o(i.SDIO_ReadFIFO)
    i.SDIO_SendCommand                       0x080056bc   Section        0  stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand)
    i.SDIO_WriteFIFO                         0x080056dc   Section        0  stm32f4xx_ll_sdmmc.o(i.SDIO_WriteFIFO)
    i.SDMMC_CmdAppCommand                    0x080056e6   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand)
    i.SDMMC_CmdAppOperCommand                0x08005718   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand)
    i.SDMMC_CmdBlockLength                   0x0800574c   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength)
    i.SDMMC_CmdBusWidth                      0x0800577c   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth)
    i.SDMMC_CmdGoIdleState                   0x080057ac   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState)
    i.SDMMC_CmdOperCond                      0x080057fc   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond)
    i.SDMMC_CmdReadMultiBlock                0x08005864   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock)
    i.SDMMC_CmdReadSingleBlock               0x08005894   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock)
    i.SDMMC_CmdSelDesel                      0x080058c4   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel)
    i.SDMMC_CmdSendCID                       0x080058f6   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID)
    i.SDMMC_CmdSendCSD                       0x08005920   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD)
    i.SDMMC_CmdSendSCR                       0x0800594c   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendSCR)
    i.SDMMC_CmdSendStatus                    0x0800597c   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus)
    i.SDMMC_CmdSetRelAdd                     0x080059ac   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd)
    i.SDMMC_CmdStopTransfer                  0x080059dc   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer)
    i.SDMMC_CmdWriteMultiBlock               0x08005a10   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock)
    i.SDMMC_CmdWriteSingleBlock              0x08005a40   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock)
    i.SDMMC_GetCmdResp1                      0x08005a70   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1)
    SDMMC_GetCmdResp1                        0x08005a71   Thumb Code   276  stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1)
    i.SDMMC_GetCmdResp2                      0x08005b8c   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2)
    SDMMC_GetCmdResp2                        0x08005b8d   Thumb Code    74  stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2)
    i.SDMMC_GetCmdResp3                      0x08005bdc   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3)
    SDMMC_GetCmdResp3                        0x08005bdd   Thumb Code    60  stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3)
    i.SDMMC_GetCmdResp6                      0x08005c1c   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6)
    SDMMC_GetCmdResp6                        0x08005c1d   Thumb Code   128  stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6)
    i.SD_FindSCR                             0x08005ca0   Section        0  stm32f4xx_hal_sd.o(i.SD_FindSCR)
    SD_FindSCR                               0x08005ca1   Thumb Code   214  stm32f4xx_hal_sd.o(i.SD_FindSCR)
    i.SD_GetCardState                        0x08005d78   Section        0  sdio_sdcard.o(i.SD_GetCardState)
    i.SD_Init                                0x08005d90   Section        0  sdio_sdcard.o(i.SD_Init)
    i.SD_InitCard                            0x08005dd4   Section        0  stm32f4xx_hal_sd.o(i.SD_InitCard)
    SD_InitCard                              0x08005dd5   Thumb Code   230  stm32f4xx_hal_sd.o(i.SD_InitCard)
    i.SD_PowerON                             0x08005eba   Section        0  stm32f4xx_hal_sd.o(i.SD_PowerON)
    SD_PowerON                               0x08005ebb   Thumb Code   162  stm32f4xx_hal_sd.o(i.SD_PowerON)
    i.SD_ReadDisk                            0x08005f5c   Section        0  sdio_sdcard.o(i.SD_ReadDisk)
    i.SD_WideBus_Disable                     0x08005f9c   Section        0  stm32f4xx_hal_sd.o(i.SD_WideBus_Disable)
    SD_WideBus_Disable                       0x08005f9d   Thumb Code    78  stm32f4xx_hal_sd.o(i.SD_WideBus_Disable)
    i.SD_WideBus_Enable                      0x08005fea   Section        0  stm32f4xx_hal_sd.o(i.SD_WideBus_Enable)
    SD_WideBus_Enable                        0x08005feb   Thumb Code    78  stm32f4xx_hal_sd.o(i.SD_WideBus_Enable)
    i.SD_WriteDisk                           0x08006038   Section        0  sdio_sdcard.o(i.SD_WriteDisk)
    i.SPI1_Init                              0x08006078   Section        0  spi.o(i.SPI1_Init)
    i.SPI1_ReadWriteByte                     0x080060c8   Section        0  spi.o(i.SPI1_ReadWriteByte)
    i.SPI1_SetSpeed                          0x080060e8   Section        0  spi.o(i.SPI1_SetSpeed)
    i.SPI_CheckFlag_BSY                      0x0800611c   Section        0  stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY)
    SPI_CheckFlag_BSY                        0x0800611d   Thumb Code    32  stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY)
    i.SPI_WaitFlagStateUntilTimeout          0x0800613c   Section        0  stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    SPI_WaitFlagStateUntilTimeout            0x0800613d   Thumb Code   150  stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    i.SRAM_Init                              0x080061d4   Section        0  sram.o(i.SRAM_Init)
    i.SVC_Handler                            0x080062dc   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.Show_Font                              0x080062e0   Section        0  text.o(i.Show_Font)
    i.Show_Str                               0x08006384   Section        0  text.o(i.Show_Str)
    i.Stm32_Clock_Init                       0x08006434   Section        0  sys.o(i.Stm32_Clock_Init)
    i.SysTick_Handler                        0x080064c8   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x080064cc   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM4_IRQHandler                        0x08006520   Section        0  usmart.o(i.TIM4_IRQHandler)
    i.TIM_Base_SetConfig                     0x08006554   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.Timer4_Init                            0x08006614   Section        0  usmart.o(i.Timer4_Init)
    i.UART_DMAAbortOnError                   0x08006668   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08006669   Thumb Code    16  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x08006678   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08006679   Thumb Code    28  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_Receive_IT                        0x08006694   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08006695   Thumb Code   140  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08006720   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08006721   Thumb Code   676  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Transmit_IT                       0x080069cc   Section        0  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    UART_Transmit_IT                         0x080069cd   Thumb Code    98  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    i.USART1_IRQHandler                      0x08006a30   Section        0  usart.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x08006a5c   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.W25QXX_Erase_Chip                      0x08006a60   Section        0  w25qxx.o(i.W25QXX_Erase_Chip)
    i.W25QXX_Erase_Sector                    0x08006a8c   Section        0  w25qxx.o(i.W25QXX_Erase_Sector)
    i.W25QXX_Init                            0x08006ae4   Section        0  w25qxx.o(i.W25QXX_Init)
    i.W25QXX_Read                            0x08006b60   Section        0  w25qxx.o(i.W25QXX_Read)
    i.W25QXX_ReadID                          0x08006bc8   Section        0  w25qxx.o(i.W25QXX_ReadID)
    i.W25QXX_ReadSR                          0x08006c0c   Section        0  w25qxx.o(i.W25QXX_ReadSR)
    i.W25QXX_Wait_Busy                       0x08006c44   Section        0  w25qxx.o(i.W25QXX_Wait_Busy)
    i.W25QXX_Write                           0x08006c54   Section        0  w25qxx.o(i.W25QXX_Write)
    i.W25QXX_Write_Enable                    0x08006d04   Section        0  w25qxx.o(i.W25QXX_Write_Enable)
    i.W25QXX_Write_NoCheck                   0x08006d24   Section        0  w25qxx.o(i.W25QXX_Write_NoCheck)
    i.W25QXX_Write_Page                      0x08006d64   Section        0  w25qxx.o(i.W25QXX_Write_Page)
    i._is_digit                              0x08006dd0   Section        0  __printf_wp.o(i._is_digit)
    i._sys_exit                              0x08006dde   Section        0  usart.o(i._sys_exit)
    i.change_bitmap                          0x08006de0   Section        0  ff.o(i.change_bitmap)
    change_bitmap                            0x08006de1   Thumb Code   118  ff.o(i.change_bitmap)
    i.check_fs                               0x08006e58   Section        0  ff.o(i.check_fs)
    check_fs                                 0x08006e59   Thumb Code   100  ff.o(i.check_fs)
    i.chk_chr                                0x08006ecc   Section        0  ff.o(i.chk_chr)
    chk_chr                                  0x08006ecd   Thumb Code    16  ff.o(i.chk_chr)
    i.clmt_clust                             0x08006edc   Section        0  ff.o(i.clmt_clust)
    clmt_clust                               0x08006edd   Thumb Code    50  ff.o(i.clmt_clust)
    i.clust2sect                             0x08006f0e   Section        0  ff.o(i.clust2sect)
    clust2sect                               0x08006f0f   Thumb Code    24  ff.o(i.clust2sect)
    i.cmp_lfn                                0x08006f28   Section        0  ff.o(i.cmp_lfn)
    cmp_lfn                                  0x08006f29   Thumb Code   128  ff.o(i.cmp_lfn)
    i.create_chain                           0x08006fac   Section        0  ff.o(i.create_chain)
    create_chain                             0x08006fad   Thumb Code   270  ff.o(i.create_chain)
    i.create_name                            0x080070bc   Section        0  ff.o(i.create_name)
    create_name                              0x080070bd   Thumb Code   514  ff.o(i.create_name)
    i.create_xdir                            0x080072d4   Section        0  ff.o(i.create_xdir)
    create_xdir                              0x080072d5   Thumb Code   124  ff.o(i.create_xdir)
    i.delay_init                             0x08007350   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x08007364   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x08007380   Section        0  delay.o(i.delay_us)
    i.dir_alloc                              0x080073b4   Section        0  ff.o(i.dir_alloc)
    dir_alloc                                0x080073b5   Thumb Code    88  ff.o(i.dir_alloc)
    i.dir_find                               0x0800740c   Section        0  ff.o(i.dir_find)
    dir_find                                 0x0800740d   Thumb Code   316  ff.o(i.dir_find)
    i.dir_next                               0x08007548   Section        0  ff.o(i.dir_next)
    dir_next                                 0x08007549   Thumb Code   260  ff.o(i.dir_next)
    i.dir_read                               0x0800764c   Section        0  ff.o(i.dir_read)
    dir_read                                 0x0800764d   Thumb Code   230  ff.o(i.dir_read)
    i.dir_register                           0x08007734   Section        0  ff.o(i.dir_register)
    dir_register                             0x08007735   Thumb Code   566  ff.o(i.dir_register)
    i.dir_remove                             0x08007970   Section        0  ff.o(i.dir_remove)
    dir_remove                               0x08007971   Thumb Code    94  ff.o(i.dir_remove)
    i.dir_sdi                                0x080079ce   Section        0  ff.o(i.dir_sdi)
    dir_sdi                                  0x080079cf   Thumb Code   150  ff.o(i.dir_sdi)
    i.disk_initialize                        0x08007a64   Section        0  diskio.o(i.disk_initialize)
    i.disk_ioctl                             0x08007a80   Section        0  diskio.o(i.disk_ioctl)
    i.disk_read                              0x08007ae0   Section        0  diskio.o(i.disk_read)
    i.disk_status                            0x08007b32   Section        0  diskio.o(i.disk_status)
    i.disk_write                             0x08007b36   Section        0  diskio.o(i.disk_write)
    i.exfuns_init                            0x08007b88   Section        0  exfuns.o(i.exfuns_init)
    i.f_close                                0x08007bec   Section        0  ff.o(i.f_close)
    i.f_closedir                             0x08007c0a   Section        0  ff.o(i.f_closedir)
    i.f_getfree                              0x08007c1e   Section        0  ff.o(i.f_getfree)
    i.f_getlabel                             0x08007d36   Section        0  ff.o(i.f_getlabel)
    i.f_gets                                 0x08007e28   Section        0  ff.o(i.f_gets)
    i.f_lseek                                0x08007e6c   Section        0  ff.o(i.f_lseek)
    i.f_mkdir                                0x08008128   Section        0  ff.o(i.f_mkdir)
    i.f_mkfs                                 0x08008304   Section        0  ff.o(i.f_mkfs)
    i.f_mount                                0x080087c8   Section        0  ff.o(i.f_mount)
    i.f_open                                 0x08008814   Section        0  ff.o(i.f_open)
    i.f_opendir                              0x08008a8a   Section        0  ff.o(i.f_opendir)
    i.f_putc                                 0x08008b5e   Section        0  ff.o(i.f_putc)
    i.f_puts                                 0x08008b9a   Section        0  ff.o(i.f_puts)
    i.f_read                                 0x08008be0   Section        0  ff.o(i.f_read)
    i.f_readdir                              0x08008d56   Section        0  ff.o(i.f_readdir)
    i.f_rename                               0x08008dcc   Section        0  ff.o(i.f_rename)
    i.f_setlabel                             0x08008f5c   Section        0  ff.o(i.f_setlabel)
    i.f_sync                                 0x0800912c   Section        0  ff.o(i.f_sync)
    i.f_unlink                               0x0800927e   Section        0  ff.o(i.f_unlink)
    i.f_write                                0x08009388   Section        0  ff.o(i.f_write)
    i.ff_convert                             0x0800955c   Section        0  mycc936.o(i.ff_convert)
    i.ff_memalloc                            0x080095cc   Section        0  diskio.o(i.ff_memalloc)
    i.ff_memfree                             0x080095d4   Section        0  diskio.o(i.ff_memfree)
    i.ff_wtoupper                            0x080095dc   Section        0  mycc936.o(i.ff_wtoupper)
    i.fill_fat_chain                         0x08009600   Section        0  ff.o(i.fill_fat_chain)
    fill_fat_chain                           0x08009601   Thumb Code    46  ff.o(i.fill_fat_chain)
    i.find_bitmap                            0x0800962e   Section        0  ff.o(i.find_bitmap)
    find_bitmap                              0x0800962f   Thumb Code   146  ff.o(i.find_bitmap)
    i.find_volume                            0x080096c0   Section        0  ff.o(i.find_volume)
    find_volume                              0x080096c1   Thumb Code   834  ff.o(i.find_volume)
    i.follow_path                            0x08009a10   Section        0  ff.o(i.follow_path)
    follow_path                              0x08009a11   Thumb Code   180  ff.o(i.follow_path)
    i.font_init                              0x08009ac4   Section        0  fontupd.o(i.font_init)
    i.fputc                                  0x08009b04   Section        0  usart.o(i.fputc)
    i.fupd_prog                              0x08009b18   Section        0  fontupd.o(i.fupd_prog)
    i.gen_numname                            0x08009b9c   Section        0  ff.o(i.gen_numname)
    gen_numname                              0x08009b9d   Thumb Code   134  ff.o(i.gen_numname)
    i.get_fat                                0x08009c28   Section        0  ff.o(i.get_fat)
    get_fat                                  0x08009c29   Thumb Code   294  ff.o(i.get_fat)
    i.get_fattime                            0x08009d4e   Section        0  diskio.o(i.get_fattime)
    i.get_fileinfo                           0x08009d52   Section        0  ff.o(i.get_fileinfo)
    get_fileinfo                             0x08009d53   Thumb Code   262  ff.o(i.get_fileinfo)
    i.get_ldnumber                           0x08009e58   Section        0  ff.o(i.get_ldnumber)
    get_ldnumber                             0x08009e59   Thumb Code    60  ff.o(i.get_ldnumber)
    i.get_xdir_info                          0x08009e94   Section        0  ff.o(i.get_xdir_info)
    get_xdir_info                            0x08009e95   Thumb Code   138  ff.o(i.get_xdir_info)
    i.ld_clust                               0x08009f1e   Section        0  ff.o(i.ld_clust)
    ld_clust                                 0x08009f1f   Thumb Code    38  ff.o(i.ld_clust)
    i.ld_dword                               0x08009f44   Section        0  ff.o(i.ld_dword)
    ld_dword                                 0x08009f45   Thumb Code    22  ff.o(i.ld_dword)
    i.ld_qword                               0x08009f5a   Section        0  ff.o(i.ld_qword)
    ld_qword                                 0x08009f5b   Thumb Code    86  ff.o(i.ld_qword)
    i.ld_word                                0x08009fb0   Section        0  ff.o(i.ld_word)
    ld_word                                  0x08009fb1   Thumb Code    10  ff.o(i.ld_word)
    i.load_obj_dir                           0x08009fba   Section        0  ff.o(i.load_obj_dir)
    load_obj_dir                             0x08009fbb   Thumb Code    56  ff.o(i.load_obj_dir)
    i.load_xdir                              0x08009ff2   Section        0  ff.o(i.load_xdir)
    load_xdir                                0x08009ff3   Thumb Code   170  ff.o(i.load_xdir)
    i.main                                   0x0800a09c   Section        0  main.o(i.main)
    i.mem_cmp                                0x0800a4e8   Section        0  ff.o(i.mem_cmp)
    mem_cmp                                  0x0800a4e9   Thumb Code    24  ff.o(i.mem_cmp)
    i.mem_cpy                                0x0800a500   Section        0  ff.o(i.mem_cpy)
    mem_cpy                                  0x0800a501   Thumb Code    18  ff.o(i.mem_cpy)
    i.mem_set                                0x0800a512   Section        0  ff.o(i.mem_set)
    mem_set                                  0x0800a513   Thumb Code    12  ff.o(i.mem_set)
    i.mf_close                               0x0800a520   Section        0  fattester.o(i.mf_close)
    i.mf_closedir                            0x0800a534   Section        0  fattester.o(i.mf_closedir)
    i.mf_fmkfs                               0x0800a540   Section        0  fattester.o(i.mf_fmkfs)
    i.mf_getlabel                            0x0800a544   Section        0  fattester.o(i.mf_getlabel)
    i.mf_gets                                0x0800a5c4   Section        0  fattester.o(i.mf_gets)
    i.mf_lseek                               0x0800a60c   Section        0  fattester.o(i.mf_lseek)
    i.mf_mkdir                               0x0800a61c   Section        0  fattester.o(i.mf_mkdir)
    i.mf_mount                               0x0800a620   Section        0  fattester.o(i.mf_mount)
    i.mf_open                                0x0800a634   Section        0  fattester.o(i.mf_open)
    i.mf_opendir                             0x0800a660   Section        0  fattester.o(i.mf_opendir)
    i.mf_putc                                0x0800a66c   Section        0  fattester.o(i.mf_putc)
    i.mf_puts                                0x0800a680   Section        0  fattester.o(i.mf_puts)
    i.mf_read                                0x0800a694   Section        0  fattester.o(i.mf_read)
    i.mf_readdir                             0x0800a7e0   Section        0  fattester.o(i.mf_readdir)
    i.mf_rename                              0x0800a988   Section        0  fattester.o(i.mf_rename)
    i.mf_scan_files                          0x0800a98c   Section        0  fattester.o(i.mf_scan_files)
    i.mf_setlabel                            0x0800a9e8   Section        0  fattester.o(i.mf_setlabel)
    i.mf_showfree                            0x0800aa3c   Section        0  fattester.o(i.mf_showfree)
    i.mf_size                                0x0800aacc   Section        0  fattester.o(i.mf_size)
    i.mf_tell                                0x0800aad8   Section        0  fattester.o(i.mf_tell)
    i.mf_unlink                              0x0800aae4   Section        0  fattester.o(i.mf_unlink)
    i.mf_write                               0x0800aae8   Section        0  fattester.o(i.mf_write)
    i.move_window                            0x0800ab9c   Section        0  ff.o(i.move_window)
    move_window                              0x0800ab9d   Thumb Code    50  ff.o(i.move_window)
    i.my_mem_free                            0x0800abd0   Section        0  malloc.o(i.my_mem_free)
    i.my_mem_init                            0x0800ac28   Section        0  malloc.o(i.my_mem_init)
    i.my_mem_malloc                          0x0800ac64   Section        0  malloc.o(i.my_mem_malloc)
    i.my_mem_perused                         0x0800acf0   Section        0  malloc.o(i.my_mem_perused)
    i.myfree                                 0x0800ad28   Section        0  malloc.o(i.myfree)
    i.mymalloc                               0x0800ad40   Section        0  malloc.o(i.mymalloc)
    i.mymemset                               0x0800ad60   Section        0  malloc.o(i.mymemset)
    i.pick_lfn                               0x0800ad6c   Section        0  ff.o(i.pick_lfn)
    pick_lfn                                 0x0800ad6d   Thumb Code   108  ff.o(i.pick_lfn)
    i.put_fat                                0x0800addc   Section        0  ff.o(i.put_fat)
    put_fat                                  0x0800addd   Thumb Code   246  ff.o(i.put_fat)
    i.putc_bfd                               0x0800aed2   Section        0  ff.o(i.putc_bfd)
    putc_bfd                                 0x0800aed3   Thumb Code    58  ff.o(i.putc_bfd)
    i.read_addr                              0x0800af0c   Section        0  usmart.o(i.read_addr)
    i.remove_chain                           0x0800af10   Section        0  ff.o(i.remove_chain)
    remove_chain                             0x0800af11   Thumb Code   232  ff.o(i.remove_chain)
    i.st_clust                               0x0800aff8   Section        0  ff.o(i.st_clust)
    st_clust                                 0x0800aff9   Thumb Code    40  ff.o(i.st_clust)
    i.st_dword                               0x0800b020   Section        0  ff.o(i.st_dword)
    st_dword                                 0x0800b021   Thumb Code    16  ff.o(i.st_dword)
    i.st_qword                               0x0800b030   Section        0  ff.o(i.st_qword)
    st_qword                                 0x0800b031   Thumb Code    74  ff.o(i.st_qword)
    i.st_word                                0x0800b07a   Section        0  ff.o(i.st_word)
    st_word                                  0x0800b07b   Thumb Code     8  ff.o(i.st_word)
    i.store_xdir                             0x0800b082   Section        0  ff.o(i.store_xdir)
    store_xdir                               0x0800b083   Thumb Code   108  ff.o(i.store_xdir)
    i.sum_sfn                                0x0800b0ee   Section        0  ff.o(i.sum_sfn)
    sum_sfn                                  0x0800b0ef   Thumb Code    26  ff.o(i.sum_sfn)
    i.sync_fs                                0x0800b108   Section        0  ff.o(i.sync_fs)
    sync_fs                                  0x0800b109   Thumb Code   126  ff.o(i.sync_fs)
    i.sync_window                            0x0800b190   Section        0  ff.o(i.sync_window)
    sync_window                              0x0800b191   Thumb Code    82  ff.o(i.sync_window)
    i.uart_init                              0x0800b1e4   Section        0  usart.o(i.uart_init)
    i.updata_fontx                           0x0800b21c   Section        0  fontupd.o(i.updata_fontx)
    i.update_font                            0x0800b30c   Section        0  fontupd.o(i.update_font)
    i.usmart_cmd_rec                         0x0800b61c   Section        0  usmart.o(i.usmart_cmd_rec)
    i.usmart_exe                             0x0800b6b0   Section        0  usmart.o(i.usmart_exe)
    i.usmart_get_aparm                       0x0800b910   Section        0  usmart_str.o(i.usmart_get_aparm)
    i.usmart_get_cmdname                     0x0800b9b4   Section        0  usmart_str.o(i.usmart_get_cmdname)
    i.usmart_get_fname                       0x0800b9e4   Section        0  usmart_str.o(i.usmart_get_fname)
    i.usmart_get_fparam                      0x0800bb4c   Section        0  usmart_str.o(i.usmart_get_fparam)
    i.usmart_get_parmpos                     0x0800bc1c   Section        0  usmart_str.o(i.usmart_get_parmpos)
    i.usmart_get_runtime                     0x0800bc40   Section        0  usmart.o(i.usmart_get_runtime)
    i.usmart_init                            0x0800bc70   Section        0  usmart.o(i.usmart_init)
    i.usmart_pow                             0x0800bc98   Section        0  usmart_str.o(i.usmart_pow)
    i.usmart_reset_runtime                   0x0800bca8   Section        0  usmart.o(i.usmart_reset_runtime)
    i.usmart_scan                            0x0800bcd0   Section        0  usmart.o(i.usmart_scan)
    i.usmart_search_nextc                    0x0800bd74   Section        0  usmart_str.o(i.usmart_search_nextc)
    i.usmart_str2num                         0x0800bd84   Section        0  usmart_str.o(i.usmart_str2num)
    i.usmart_strcmp                          0x0800be5a   Section        0  usmart_str.o(i.usmart_strcmp)
    i.usmart_strcopy                         0x0800be72   Section        0  usmart_str.o(i.usmart_strcopy)
    i.usmart_strlen                          0x0800be84   Section        0  usmart_str.o(i.usmart_strlen)
    i.usmart_sys_cmd_exe                     0x0800be98   Section        0  usmart.o(i.usmart_sys_cmd_exe)
    i.validate                               0x0800c518   Section        0  ff.o(i.validate)
    validate                                 0x0800c519   Thumb Code    50  ff.o(i.validate)
    i.write_addr                             0x0800c54a   Section        0  usmart.o(i.write_addr)
    i.xdir_sum                               0x0800c54e   Section        0  ff.o(i.xdir_sum)
    xdir_sum                                 0x0800c54f   Thumb Code    52  ff.o(i.xdir_sum)
    i.xname_sum                              0x0800c582   Section        0  ff.o(i.xname_sum)
    xname_sum                                0x0800c583   Thumb Code    60  ff.o(i.xname_sum)
    x$fpl$dfixu                              0x0800c5c0   Section       90  dfixu.o(x$fpl$dfixu)
    $v0                                      0x0800c5c0   Number         0  dfixu.o(x$fpl$dfixu)
    x$fpl$dfltu                              0x0800c61a   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x0800c61a   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dmul                               0x0800c640   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x0800c640   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x0800c794   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x0800c794   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x0800c830   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x0800c830   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$fpinit                             0x0800c83c   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800c83c   Number         0  fpinit.o(x$fpl$fpinit)
    .constdata                               0x0800c846   Section       16  system_stm32f4xx.o(.constdata)
    x$fpl$usenofp                            0x0800c846   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x0800c856   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x0800c85e   Section    18240  lcd.o(.constdata)
    .constdata                               0x08010fa0   Section       36  malloc.o(.constdata)
    .constdata                               0x08010fc4   Section       74  ff.o(.constdata)
    LfnOfs                                   0x08010fc4   Data          13  ff.o(.constdata)
    badchr                                   0x08010fd1   Data          16  ff.o(.constdata)
    vst                                      0x08010fe2   Data          22  ff.o(.constdata)
    cst                                      0x08010ff8   Data          22  ff.o(.constdata)
    .constdata                               0x0801100e   Section      960  mycc936.o(.constdata)
    tbl_lower                                0x0801100e   Data         480  mycc936.o(.constdata)
    tbl_upper                                0x080111ee   Data         480  mycc936.o(.constdata)
    .constdata                               0x080113d0   Section       16  fontupd.o(.constdata)
    .constdata                               0x080113e0   Section       40  _printf_hex_int.o(.constdata)
    uc_hextab                                0x080113e0   Data          20  _printf_hex_int.o(.constdata)
    lc_hextab                                0x080113f4   Data          20  _printf_hex_int.o(.constdata)
    .constdata                               0x08011408   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x08011408   Data          17  __printf_flags_ss_wp.o(.constdata)
    .conststring                             0x0801141c   Section       40  usmart.o(.conststring)
    .conststring                             0x08011444   Section      649  usmart_config.o(.conststring)
    .conststring                             0x080116d0   Section       95  fontupd.o(.conststring)
    .ARM.__AT_0x10000000                     0x10000000   Section    61440  malloc.o(.ARM.__AT_0x10000000)
    .ARM.__AT_0x1000F000                     0x1000f000   Section     3840  malloc.o(.ARM.__AT_0x1000F000)
    .data                                    0x20000000   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000004   Section        4  stm32f4xx_hal.o(.data)
    .data                                    0x20000008   Section        4  delay.o(.data)
    fac_us                                   0x20000008   Data           4  delay.o(.data)
    .data                                    0x2000000c   Section        4  usart.o(.data)
    .data                                    0x20000010   Section        4  usart.o(.data)
    .data                                    0x20000014   Section        8  lcd.o(.data)
    .data                                    0x2000001c   Section        1  key.o(.data)
    key_up                                   0x2000001c   Data           1  key.o(.data)
    .data                                    0x2000001e   Section        2  w25qxx.o(.data)
    .data                                    0x20000020   Section       28  usmart.o(.data)
    .data                                    0x2000003c   Section      200  usmart_config.o(.data)
    .data                                    0x20000104   Section      244  usmart_config.o(.data)
    .data                                    0x200001f8   Section       36  malloc.o(.data)
    .data                                    0x2000021c   Section       12  ff.o(.data)
    Fsid                                     0x2000021c   Data           2  ff.o(.data)
    FatFs                                    0x20000220   Data           8  ff.o(.data)
    .data                                    0x20000228   Section        2  diskio.o(.data)
    .data                                    0x2000022c   Section       20  exfuns.o(.data)
    .data                                    0x20000240   Section        4  exfuns.o(.data)
    .data                                    0x20000244   Section        4  exfuns.o(.data)
    .bss                                     0x20000248   Section      264  usart.o(.bss)
    .bss                                     0x20000350   Section       94  lcd.o(.bss)
    .bss                                     0x200003b0   Section       80  sram.o(.bss)
    .bss                                     0x20000400   Section       88  spi.o(.bss)
    .bss                                     0x20000458   Section     4096  w25qxx.o(.bss)
    .bss                                     0x20001458   Section      164  sdio_sdcard.o(.bss)
    .bss                                     0x200014fc   Section       60  usmart.o(.bss)
    .bss                                     0x20001540   Section    102400  malloc.o(.bss)
    .bss                                     0x2001a540   Section     6400  malloc.o(.bss)
    .bss                                     0x2001be40   Section      288  exfuns.o(.bss)
    .bss                                     0x2001bf60   Section       72  exfuns.o(.bss)
    .bss                                     0x2001bfa8   Section       33  fontupd.o(.bss)
    .bss                                     0x2001bfcc   Section       96  libspace.o(.bss)
    HEAP                                     0x2001c030   Section      512  startup_stm32f407xx.o(HEAP)
    Heap_Mem                                 0x2001c030   Data         512  startup_stm32f407xx.o(HEAP)
    STACK                                    0x2001c230   Section     1024  startup_stm32f407xx.o(STACK)
    Stack_Mem                                0x2001c230   Data        1024  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x2001c630   Data           0  startup_stm32f407xx.o(STACK)
    .ARM.__AT_0x68000000                     0x68000000   Section    983040  malloc.o(.ARM.__AT_0x68000000)
    .ARM.__AT_0x680F0000                     0x680f0000   Section    61440  malloc.o(.ARM.__AT_0x680F0000)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001c5   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x080001c5   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000221   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_ll                               0x0800023d   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_percent                          0x0800023d   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x08000247   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x0800024d   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_x                                0x08000253   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lld                              0x08000259   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_c                                0x0800025f   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x08000265   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x0800026b   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0800026f   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000271   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x08000275   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000275   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000275   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000275   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000275   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000275   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000275   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000275   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000275   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000275   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000275   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000275   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000275   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000275   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000275   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000275   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000275   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000275   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000275   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000275   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000277   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000279   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000279   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000279   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000279   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000279   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000279   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000279   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x0800027b   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0800027b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0800027b   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000281   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000281   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000285   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000285   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800028d   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800028f   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800028f   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000293   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    WFI_SET                                  0x08000299   Thumb Code     2  sys.o(.emb_text)
    INTX_DISABLE                             0x0800029b   Thumb Code     4  sys.o(.emb_text)
    INTX_ENABLE                              0x0800029f   Thumb Code     4  sys.o(.emb_text)
    MSR_MSP                                  0x080002a3   Thumb Code     6  sys.o(.emb_text)
    Reset_Handler                            0x080002a9   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080002c3   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x080002c5   Thumb Code     0  startup_stm32f407xx.o(.text)
    __use_no_semihosting                     0x080002e9   Thumb Code     2  use_no_semi_2.o(.text)
    __aeabi_uldivmod                         0x080002eb   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x080002eb   Thumb Code   238  lludivv7m.o(.text)
    __2printf                                0x080003d9   Thumb Code    20  noretval__2printf.o(.text)
    _printf_pre_padding                      0x080003f1   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x0800041d   Thumb Code    34  _printf_pad.o(.text)
    _printf_str                              0x0800043f   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08000491   Thumb Code   104  _printf_dec.o(.text)
    _printf_longlong_dec                     0x08000509   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_int_hex                          0x08000585   Thumb Code    84  _printf_hex_int.o(.text)
    _printf_longlong_hex                     0x08000585   Thumb Code     0  _printf_hex_int.o(.text)
    __printf                                 0x080005dd   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    strcpy                                   0x08000765   Thumb Code    72  strcpy.o(.text)
    strcat                                   0x080007ad   Thumb Code    24  strcat.o(.text)
    __use_two_region_memory                  0x080007c5   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080007c7   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080007c9   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x080007cb   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080007cb   Thumb Code     2  use_no_semi.o(.text)
    _ll_udiv10                               0x080007cd   Thumb Code   138  lludiv10.o(.text)
    _printf_int_common                       0x08000857   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_cs_common                        0x08000909   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x0800091d   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x0800092d   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x08000935   Thumb Code    32  _printf_char_file.o(.text)
    _printf_char_common                      0x08000963   Thumb Code    32  _printf_char_common.o(.text)
    ferror                                   0x08000989   Thumb Code     8  ferror.o(.text)
    __user_setup_stackheap                   0x08000991   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x080009db   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x080009ed   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080009ed   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080009ed   Thumb Code     0  libspace.o(.text)
    BusFault_Handler                         0x080009f5   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x080009f7   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    FSMC_NORSRAM_Extended_Timing_Init        0x080009f9   Thumb Code    60  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Extended_Timing_Init)
    FSMC_NORSRAM_Init                        0x08000a39   Thumb Code    80  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Init)
    FSMC_NORSRAM_Timing_Init                 0x08000a8d   Thumb Code    64  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Timing_Init)
    Get_HzMat                                0x08000ad1   Thumb Code   136  text.o(i.Get_HzMat)
    HAL_DMA_Abort_IT                         0x08000b5d   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_Delay                                0x08000b81   Thumb Code    28  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08000b9d   Thumb Code   402  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08000d5d   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x08000d67   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetREVID                             0x08000d71   Thumb Code     8  stm32f4xx_hal.o(i.HAL_GetREVID)
    HAL_GetTick                              0x08000d7d   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08000d89   Thumb Code    10  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08000d99   Thumb Code    40  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08000dc5   Thumb Code    34  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08000ded   Thumb Code     2  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08000def   Thumb Code    22  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08000e05   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08000e45   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08000e69   Thumb Code   356  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08000fdd   Thumb Code    34  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x0800100d   Thumb Code    34  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x0800103d   Thumb Code   104  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x080010ad   Thumb Code   766  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SD_ConfigWideBusOperation            0x080013c1   Thumb Code   120  stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation)
    HAL_SD_GetCardCSD                        0x08001439   Thumb Code   474  stm32f4xx_hal_sd.o(i.HAL_SD_GetCardCSD)
    HAL_SD_GetCardInfo                       0x08001613   Thumb Code    36  stm32f4xx_hal_sd.o(i.HAL_SD_GetCardInfo)
    HAL_SD_GetCardState                      0x08001637   Thumb Code    50  stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState)
    HAL_SD_Init                              0x08001669   Thumb Code    54  stm32f4xx_hal_sd.o(i.HAL_SD_Init)
    HAL_SD_InitCard                          0x080016a1   Thumb Code    80  stm32f4xx_hal_sd.o(i.HAL_SD_InitCard)
    HAL_SD_MspInit                           0x080016f5   Thumb Code   168  sdio_sdcard.o(i.HAL_SD_MspInit)
    HAL_SD_ReadBlocks                        0x080017ad   Thumb Code   436  stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks)
    HAL_SD_WriteBlocks                       0x08001961   Thumb Code   390  stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks)
    HAL_SPI_Init                             0x08001ae7   Thumb Code   140  stm32f4xx_hal_spi.o(i.HAL_SPI_Init)
    HAL_SPI_MspInit                          0x08001b75   Thumb Code    72  spi.o(i.HAL_SPI_MspInit)
    HAL_SPI_TransmitReceive                  0x08001bc5   Thumb Code   608  stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive)
    HAL_SRAM_Init                            0x08001e25   Thumb Code    86  stm32f4xx_hal_sram.o(i.HAL_SRAM_Init)
    HAL_SRAM_MspInit                         0x08001e7d   Thumb Code   162  lcd.o(i.HAL_SRAM_MspInit)
    HAL_SYSTICK_CLKSourceConfig              0x08001f35   Thumb Code    24  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig)
    HAL_SYSTICK_Config                       0x08001f4d   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIM_Base_Init                        0x08001f75   Thumb Code    54  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08001fab   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08001fad   Thumb Code    24  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_UART_ErrorCallback                   0x08001fc5   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_GetState                        0x08001fc7   Thumb Code    10  stm32f4xx_hal_uart.o(i.HAL_UART_GetState)
    HAL_UART_IRQHandler                      0x08001fd1   Thumb Code   266  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x080020e1   Thumb Code    98  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08002145   Thumb Code   112  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x080021c1   Thumb Code    72  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08002209   Thumb Code    76  usart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_TxCpltCallback                  0x08002261   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08002265   Thumb Code     8  stm32f4xx_it.o(i.HardFault_Handler)
    KEY_Init                                 0x08002281   Thumb Code    80  key.o(i.KEY_Init)
    KEY_Scan                                 0x080022dd   Thumb Code   116  key.o(i.KEY_Scan)
    LCD_Clear                                0x0800235d   Thumb Code    46  lcd.o(i.LCD_Clear)
    LCD_Display_Dir                          0x08002391   Thumb Code   226  lcd.o(i.LCD_Display_Dir)
    LCD_Fast_DrawPoint                       0x08002479   Thumb Code   202  lcd.o(i.LCD_Fast_DrawPoint)
    LCD_Fill                                 0x08002549   Thumb Code    70  lcd.o(i.LCD_Fill)
    LCD_Init                                 0x08002591   Thumb Code 11076  lcd.o(i.LCD_Init)
    LCD_Pow                                  0x080050d9   Thumb Code    16  lcd.o(i.LCD_Pow)
    LCD_RD_DATA                              0x080050e9   Thumb Code    20  lcd.o(i.LCD_RD_DATA)
    LCD_SSD_BackLightSet                     0x080050fd   Thumb Code    72  lcd.o(i.LCD_SSD_BackLightSet)
    LCD_Scan_Dir                             0x0800514d   Thumb Code   418  lcd.o(i.LCD_Scan_Dir)
    LCD_SetCursor                            0x080052f5   Thumb Code   236  lcd.o(i.LCD_SetCursor)
    LCD_ShowChar                             0x080053e5   Thumb Code   196  lcd.o(i.LCD_ShowChar)
    LCD_ShowNum                              0x080054c1   Thumb Code   118  lcd.o(i.LCD_ShowNum)
    LCD_ShowString                           0x08005537   Thumb Code    84  lcd.o(i.LCD_ShowString)
    LCD_WR_DATA                              0x0800558b   Thumb Code    24  lcd.o(i.LCD_WR_DATA)
    LCD_WR_REG                               0x080055a3   Thumb Code    24  lcd.o(i.LCD_WR_REG)
    LCD_WriteRAM_Prepare                     0x080055bd   Thumb Code    14  lcd.o(i.LCD_WriteRAM_Prepare)
    LCD_WriteReg                             0x080055d1   Thumb Code    12  lcd.o(i.LCD_WriteReg)
    LED_Init                                 0x080055dd   Thumb Code    72  led.o(i.LED_Init)
    MemManage_Handler                        0x0800562d   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x0800562f   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08005651   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    SDIO_ConfigData                          0x08005653   Thumb Code    36  stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData)
    SDIO_GetPowerState                       0x08005677   Thumb Code     8  stm32f4xx_ll_sdmmc.o(i.SDIO_GetPowerState)
    SDIO_GetResponse                         0x0800567f   Thumb Code    12  stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse)
    SDIO_Init                                0x0800568b   Thumb Code    36  stm32f4xx_ll_sdmmc.o(i.SDIO_Init)
    SDIO_PowerState_ON                       0x080056af   Thumb Code     8  stm32f4xx_ll_sdmmc.o(i.SDIO_PowerState_ON)
    SDIO_ReadFIFO                            0x080056b7   Thumb Code     6  stm32f4xx_ll_sdmmc.o(i.SDIO_ReadFIFO)
    SDIO_SendCommand                         0x080056bd   Thumb Code    32  stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand)
    SDIO_WriteFIFO                           0x080056dd   Thumb Code    10  stm32f4xx_ll_sdmmc.o(i.SDIO_WriteFIFO)
    SDMMC_CmdAppCommand                      0x080056e7   Thumb Code    48  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand)
    SDMMC_CmdAppOperCommand                  0x08005719   Thumb Code    46  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand)
    SDMMC_CmdBlockLength                     0x0800574d   Thumb Code    48  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength)
    SDMMC_CmdBusWidth                        0x0800577d   Thumb Code    48  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth)
    SDMMC_CmdGoIdleState                     0x080057ad   Thumb Code    76  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState)
    SDMMC_CmdOperCond                        0x080057fd   Thumb Code    98  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond)
    SDMMC_CmdReadMultiBlock                  0x08005865   Thumb Code    48  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock)
    SDMMC_CmdReadSingleBlock                 0x08005895   Thumb Code    48  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock)
    SDMMC_CmdSelDesel                        0x080058c5   Thumb Code    50  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel)
    SDMMC_CmdSendCID                         0x080058f7   Thumb Code    42  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID)
    SDMMC_CmdSendCSD                         0x08005921   Thumb Code    44  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD)
    SDMMC_CmdSendSCR                         0x0800594d   Thumb Code    48  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendSCR)
    SDMMC_CmdSendStatus                      0x0800597d   Thumb Code    48  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus)
    SDMMC_CmdSetRelAdd                       0x080059ad   Thumb Code    48  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd)
    SDMMC_CmdStopTransfer                    0x080059dd   Thumb Code    46  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer)
    SDMMC_CmdWriteMultiBlock                 0x08005a11   Thumb Code    48  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock)
    SDMMC_CmdWriteSingleBlock                0x08005a41   Thumb Code    48  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock)
    SD_GetCardState                          0x08005d79   Thumb Code    20  sdio_sdcard.o(i.SD_GetCardState)
    SD_Init                                  0x08005d91   Thumb Code    60  sdio_sdcard.o(i.SD_Init)
    SD_ReadDisk                              0x08005f5d   Thumb Code    54  sdio_sdcard.o(i.SD_ReadDisk)
    SD_WriteDisk                             0x08006039   Thumb Code    54  sdio_sdcard.o(i.SD_WriteDisk)
    SPI1_Init                                0x08006079   Thumb Code    72  spi.o(i.SPI1_Init)
    SPI1_ReadWriteByte                       0x080060c9   Thumb Code    28  spi.o(i.SPI1_ReadWriteByte)
    SPI1_SetSpeed                            0x080060e9   Thumb Code    46  spi.o(i.SPI1_SetSpeed)
    SRAM_Init                                0x080061d5   Thumb Code   236  sram.o(i.SRAM_Init)
    SVC_Handler                              0x080062dd   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    Show_Font                                0x080062e1   Thumb Code   156  text.o(i.Show_Font)
    Show_Str                                 0x08006385   Thumb Code   174  text.o(i.Show_Str)
    Stm32_Clock_Init                         0x08006435   Thumb Code   136  sys.o(i.Stm32_Clock_Init)
    SysTick_Handler                          0x080064c9   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x080064cd   Thumb Code    66  system_stm32f4xx.o(i.SystemInit)
    TIM4_IRQHandler                          0x08006521   Thumb Code    44  usmart.o(i.TIM4_IRQHandler)
    TIM_Base_SetConfig                       0x08006555   Thumb Code   148  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    Timer4_Init                              0x08006615   Thumb Code    72  usmart.o(i.Timer4_Init)
    USART1_IRQHandler                        0x08006a31   Thumb Code    34  usart.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x08006a5d   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    W25QXX_Erase_Chip                        0x08006a61   Thumb Code    40  w25qxx.o(i.W25QXX_Erase_Chip)
    W25QXX_Erase_Sector                      0x08006a8d   Thumb Code    80  w25qxx.o(i.W25QXX_Erase_Sector)
    W25QXX_Init                              0x08006ae5   Thumb Code   108  w25qxx.o(i.W25QXX_Init)
    W25QXX_Read                              0x08006b61   Thumb Code    94  w25qxx.o(i.W25QXX_Read)
    W25QXX_ReadID                            0x08006bc9   Thumb Code    62  w25qxx.o(i.W25QXX_ReadID)
    W25QXX_ReadSR                            0x08006c0d   Thumb Code    52  w25qxx.o(i.W25QXX_ReadSR)
    W25QXX_Wait_Busy                         0x08006c45   Thumb Code    14  w25qxx.o(i.W25QXX_Wait_Busy)
    W25QXX_Write                             0x08006c55   Thumb Code   154  w25qxx.o(i.W25QXX_Write)
    W25QXX_Write_Enable                      0x08006d05   Thumb Code    26  w25qxx.o(i.W25QXX_Write_Enable)
    W25QXX_Write_NoCheck                     0x08006d25   Thumb Code    62  w25qxx.o(i.W25QXX_Write_NoCheck)
    W25QXX_Write_Page                        0x08006d65   Thumb Code   100  w25qxx.o(i.W25QXX_Write_Page)
    _is_digit                                0x08006dd1   Thumb Code    14  __printf_wp.o(i._is_digit)
    _sys_exit                                0x08006ddf   Thumb Code     2  usart.o(i._sys_exit)
    delay_init                               0x08007351   Thumb Code    16  delay.o(i.delay_init)
    delay_ms                                 0x08007365   Thumb Code    26  delay.o(i.delay_ms)
    delay_us                                 0x08007381   Thumb Code    48  delay.o(i.delay_us)
    disk_initialize                          0x08007a65   Thumb Code    28  diskio.o(i.disk_initialize)
    disk_ioctl                               0x08007a81   Thumb Code    86  diskio.o(i.disk_ioctl)
    disk_read                                0x08007ae1   Thumb Code    82  diskio.o(i.disk_read)
    disk_status                              0x08007b33   Thumb Code     4  diskio.o(i.disk_status)
    disk_write                               0x08007b37   Thumb Code    82  diskio.o(i.disk_write)
    exfuns_init                              0x08007b89   Thumb Code    94  exfuns.o(i.exfuns_init)
    f_close                                  0x08007bed   Thumb Code    30  ff.o(i.f_close)
    f_closedir                               0x08007c0b   Thumb Code    20  ff.o(i.f_closedir)
    f_getfree                                0x08007c1f   Thumb Code   280  ff.o(i.f_getfree)
    f_getlabel                               0x08007d37   Thumb Code   242  ff.o(i.f_getlabel)
    f_gets                                   0x08007e29   Thumb Code    68  ff.o(i.f_gets)
    f_lseek                                  0x08007e6d   Thumb Code   700  ff.o(i.f_lseek)
    f_mkdir                                  0x08008129   Thumb Code   476  ff.o(i.f_mkdir)
    f_mkfs                                   0x08008305   Thumb Code  1216  ff.o(i.f_mkfs)
    f_mount                                  0x080087c9   Thumb Code    72  ff.o(i.f_mount)
    f_open                                   0x08008815   Thumb Code   630  ff.o(i.f_open)
    f_opendir                                0x08008a8b   Thumb Code   212  ff.o(i.f_opendir)
    f_putc                                   0x08008b5f   Thumb Code    60  ff.o(i.f_putc)
    f_puts                                   0x08008b9b   Thumb Code    70  ff.o(i.f_puts)
    f_read                                   0x08008be1   Thumb Code   374  ff.o(i.f_read)
    f_readdir                                0x08008d57   Thumb Code   118  ff.o(i.f_readdir)
    f_rename                                 0x08008dcd   Thumb Code   398  ff.o(i.f_rename)
    f_setlabel                               0x08008f5d   Thumb Code   460  ff.o(i.f_setlabel)
    f_sync                                   0x0800912d   Thumb Code   338  ff.o(i.f_sync)
    f_unlink                                 0x0800927f   Thumb Code   266  ff.o(i.f_unlink)
    f_write                                  0x08009389   Thumb Code   468  ff.o(i.f_write)
    ff_convert                               0x0800955d   Thumb Code   106  mycc936.o(i.ff_convert)
    ff_memalloc                              0x080095cd   Thumb Code     8  diskio.o(i.ff_memalloc)
    ff_memfree                               0x080095d5   Thumb Code     8  diskio.o(i.ff_memfree)
    ff_wtoupper                              0x080095dd   Thumb Code    28  mycc936.o(i.ff_wtoupper)
    font_init                                0x08009ac5   Thumb Code    58  fontupd.o(i.font_init)
    fputc                                    0x08009b05   Thumb Code    14  usart.o(i.fputc)
    fupd_prog                                0x08009b19   Thumb Code   120  fontupd.o(i.fupd_prog)
    get_fattime                              0x08009d4f   Thumb Code     4  diskio.o(i.get_fattime)
    main                                     0x0800a09d   Thumb Code   784  main.o(i.main)
    mf_close                                 0x0800a521   Thumb Code    14  fattester.o(i.mf_close)
    mf_closedir                              0x0800a535   Thumb Code     6  fattester.o(i.mf_closedir)
    mf_fmkfs                                 0x0800a541   Thumb Code     4  fattester.o(i.mf_fmkfs)
    mf_getlabel                              0x0800a545   Thumb Code    56  fattester.o(i.mf_getlabel)
    mf_gets                                  0x0800a5c5   Thumb Code    36  fattester.o(i.mf_gets)
    mf_lseek                                 0x0800a60d   Thumb Code    12  fattester.o(i.mf_lseek)
    mf_mkdir                                 0x0800a61d   Thumb Code     4  fattester.o(i.mf_mkdir)
    mf_mount                                 0x0800a621   Thumb Code    14  fattester.o(i.mf_mount)
    mf_open                                  0x0800a635   Thumb Code    28  fattester.o(i.mf_open)
    mf_opendir                               0x0800a661   Thumb Code     8  fattester.o(i.mf_opendir)
    mf_putc                                  0x0800a66d   Thumb Code    14  fattester.o(i.mf_putc)
    mf_puts                                  0x0800a681   Thumb Code    14  fattester.o(i.mf_puts)
    mf_read                                  0x0800a695   Thumb Code   210  fattester.o(i.mf_read)
    mf_readdir                               0x0800a7e1   Thumb Code   150  fattester.o(i.mf_readdir)
    mf_rename                                0x0800a989   Thumb Code     4  fattester.o(i.mf_rename)
    mf_scan_files                            0x0800a98d   Thumb Code    66  fattester.o(i.mf_scan_files)
    mf_setlabel                              0x0800a9e9   Thumb Code    28  fattester.o(i.mf_setlabel)
    mf_showfree                              0x0800aa3d   Thumb Code    64  fattester.o(i.mf_showfree)
    mf_size                                  0x0800aacd   Thumb Code     8  fattester.o(i.mf_size)
    mf_tell                                  0x0800aad9   Thumb Code     8  fattester.o(i.mf_tell)
    mf_unlink                                0x0800aae5   Thumb Code     4  fattester.o(i.mf_unlink)
    mf_write                                 0x0800aae9   Thumb Code    64  fattester.o(i.mf_write)
    my_mem_free                              0x0800abd1   Thumb Code    80  malloc.o(i.my_mem_free)
    my_mem_init                              0x0800ac29   Thumb Code    52  malloc.o(i.my_mem_init)
    my_mem_malloc                            0x0800ac65   Thumb Code   132  malloc.o(i.my_mem_malloc)
    my_mem_perused                           0x0800acf1   Thumb Code    48  malloc.o(i.my_mem_perused)
    myfree                                   0x0800ad29   Thumb Code    20  malloc.o(i.myfree)
    mymalloc                                 0x0800ad41   Thumb Code    28  malloc.o(i.mymalloc)
    mymemset                                 0x0800ad61   Thumb Code    12  malloc.o(i.mymemset)
    read_addr                                0x0800af0d   Thumb Code     4  usmart.o(i.read_addr)
    uart_init                                0x0800b1e5   Thumb Code    44  usart.o(i.uart_init)
    updata_fontx                             0x0800b21d   Thumb Code   232  fontupd.o(i.updata_fontx)
    update_font                              0x0800b30d   Thumb Code   664  fontupd.o(i.update_font)
    usmart_cmd_rec                           0x0800b61d   Thumb Code   144  usmart.o(i.usmart_cmd_rec)
    usmart_exe                               0x0800b6b1   Thumb Code   510  usmart.o(i.usmart_exe)
    usmart_get_aparm                         0x0800b911   Thumb Code   164  usmart_str.o(i.usmart_get_aparm)
    usmart_get_cmdname                       0x0800b9b5   Thumb Code    46  usmart_str.o(i.usmart_get_cmdname)
    usmart_get_fname                         0x0800b9e5   Thumb Code   352  usmart_str.o(i.usmart_get_fname)
    usmart_get_fparam                        0x0800bb4d   Thumb Code   202  usmart_str.o(i.usmart_get_fparam)
    usmart_get_parmpos                       0x0800bc1d   Thumb Code    30  usmart_str.o(i.usmart_get_parmpos)
    usmart_get_runtime                       0x0800bc41   Thumb Code    38  usmart.o(i.usmart_get_runtime)
    usmart_init                              0x0800bc71   Thumb Code    36  usmart.o(i.usmart_init)
    usmart_pow                               0x0800bc99   Thumb Code    16  usmart_str.o(i.usmart_pow)
    usmart_reset_runtime                     0x0800bca9   Thumb Code    32  usmart.o(i.usmart_reset_runtime)
    usmart_scan                              0x0800bcd1   Thumb Code    94  usmart.o(i.usmart_scan)
    usmart_search_nextc                      0x0800bd75   Thumb Code    16  usmart_str.o(i.usmart_search_nextc)
    usmart_str2num                           0x0800bd85   Thumb Code   214  usmart_str.o(i.usmart_str2num)
    usmart_strcmp                            0x0800be5b   Thumb Code    24  usmart_str.o(i.usmart_strcmp)
    usmart_strcopy                           0x0800be73   Thumb Code    18  usmart_str.o(i.usmart_strcopy)
    usmart_strlen                            0x0800be85   Thumb Code    18  usmart_str.o(i.usmart_strlen)
    usmart_sys_cmd_exe                       0x0800be99   Thumb Code  1458  usmart.o(i.usmart_sys_cmd_exe)
    write_addr                               0x0800c54b   Thumb Code     4  usmart.o(i.write_addr)
    __aeabi_d2uiz                            0x0800c5c1   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x0800c5c1   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_ui2d                             0x0800c61b   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x0800c61b   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_dmul                             0x0800c641   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x0800c641   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x0800c795   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x0800c831   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    _fp_init                                 0x0800c83d   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x0800c845   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x0800c845   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    AHBPrescTable                            0x0800c846   Data          16  system_stm32f4xx.o(.constdata)
    __I$use$fp                               0x0800c846   Number         0  usenofp.o(x$fpl$usenofp)
    APBPrescTable                            0x0800c856   Data           8  system_stm32f4xx.o(.constdata)
    asc2_1206                                0x0800c85e   Data        1140  lcd.o(.constdata)
    asc2_1608                                0x0800ccd2   Data        1520  lcd.o(.constdata)
    asc2_2412                                0x0800d2c2   Data        3420  lcd.o(.constdata)
    asc2_3216                                0x0800e01e   Data       12160  lcd.o(.constdata)
    memtblsize                               0x08010fa0   Data          12  malloc.o(.constdata)
    memblksize                               0x08010fac   Data          12  malloc.o(.constdata)
    memsize                                  0x08010fb8   Data          12  malloc.o(.constdata)
    GBK24_PATH                               0x080113d0   Data           4  fontupd.o(.constdata)
    GBK16_PATH                               0x080113d4   Data           4  fontupd.o(.constdata)
    GBK12_PATH                               0x080113d8   Data           4  fontupd.o(.constdata)
    UNIGBK_PATH                              0x080113dc   Data           4  fontupd.o(.constdata)
    Region$$Table$$Base                      0x08011730   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08011750   Number         0  anon$$obj.o(Region$$Table)
    mem3base                                 0x10000000   Data       61440  malloc.o(.ARM.__AT_0x10000000)
    mem3mapbase                              0x1000f000   Data        3840  malloc.o(.ARM.__AT_0x1000F000)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f4xx.o(.data)
    uwTick                                   0x20000004   Data           4  stm32f4xx_hal.o(.data)
    aRxBuffer                                0x2000000c   Data           1  usart.o(.data)
    USART_RX_STA                             0x2000000e   Data           2  usart.o(.data)
    __stdout                                 0x20000010   Data           4  usart.o(.data)
    POINT_COLOR                              0x20000014   Data           4  lcd.o(.data)
    BACK_COLOR                               0x20000018   Data           4  lcd.o(.data)
    W25QXX_TYPE                              0x2000001e   Data           2  w25qxx.o(.data)
    sys_cmd_tab                              0x20000020   Data          28  usmart.o(.data)
    usmart_nametab                           0x2000003c   Data         200  usmart_config.o(.data)
    usmart_dev                               0x20000104   Data         244  usmart_config.o(.data)
    mallco_dev                               0x200001f8   Data          36  malloc.o(.data)
    FLASH_SECTOR_COUNT                       0x20000228   Data           2  diskio.o(.data)
    file                                     0x2000022c   Data           4  exfuns.o(.data)
    ftemp                                    0x20000230   Data           4  exfuns.o(.data)
    fatbuf                                   0x20000234   Data           4  exfuns.o(.data)
    fs                                       0x20000238   Data           8  exfuns.o(.data)
    br                                       0x20000240   Data           4  exfuns.o(.data)
    bw                                       0x20000244   Data           4  exfuns.o(.data)
    USART_RX_BUF                             0x20000248   Data         200  usart.o(.bss)
    UART1_Handler                            0x20000310   Data          64  usart.o(.bss)
    TFTSRAM_Handler                          0x20000350   Data          80  lcd.o(.bss)
    lcddev                                   0x200003a0   Data          14  lcd.o(.bss)
    SRAM_Handler                             0x200003b0   Data          80  sram.o(.bss)
    SPI1_Handler                             0x20000400   Data          88  spi.o(.bss)
    W25QXX_BUFFER                            0x20000458   Data        4096  w25qxx.o(.bss)
    SDCARD_Handler                           0x20001458   Data         132  sdio_sdcard.o(.bss)
    SDCardInfo                               0x200014dc   Data          32  sdio_sdcard.o(.bss)
    TIM4_Handler                             0x200014fc   Data          60  usmart.o(.bss)
    mem1base                                 0x20001540   Data       102400  malloc.o(.bss)
    mem1mapbase                              0x2001a540   Data        6400  malloc.o(.bss)
    fileinfo                                 0x2001be40   Data         288  exfuns.o(.bss)
    dir                                      0x2001bf60   Data          72  exfuns.o(.bss)
    ftinfo                                   0x2001bfa8   Data          33  fontupd.o(.bss)
    __libspace_start                         0x2001bfcc   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x2001c02c   Data           0  libspace.o(.bss)
    mem2base                                 0x68000000   Data       983040  malloc.o(.ARM.__AT_0x68000000)
    mem2mapbase                              0x680f0000   Data       61440  malloc.o(.ARM.__AT_0x680F0000)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00011998, Max: 0x00100000, ABSOLUTE, COMPRESSED[0x00011848])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00011750, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          531    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000008   Code   RO         5757  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         5959    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000005a   Code   RO         5957    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x0800021e   0x0800021e   0x00000002   PAD
    0x08000220   0x08000220   0x0000001c   Code   RO         5961    !!handler_zi        c_w.l(__scatter_zi.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         5745    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x0800023c   0x0800023c   0x0000000a   Code   RO         5787    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x08000246   0x08000246   0x00000006   Code   RO         5743    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x0800024c   0x0800024c   0x00000006   Code   RO         5744    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x08000252   0x08000252   0x00000006   Code   RO         5742    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000258   0x08000258   0x00000006   Code   RO         5746    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x0800025e   0x0800025e   0x00000006   Code   RO         5740    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x08000264   0x08000264   0x00000006   Code   RO         5741    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800026a   0x0800026a   0x00000004   Code   RO         5786    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x0800026e   0x0800026e   0x00000002   Code   RO         5831    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000270   0x08000270   0x00000004   Code   RO         5843    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5846    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5849    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5851    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5853    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5856    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5858    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5860    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5862    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5864    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5866    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5868    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5870    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5872    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5874    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5876    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5880    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5882    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5884    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         5886    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000274   0x08000274   0x00000002   Code   RO         5887    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000276   0x08000276   0x00000002   Code   RO         5913    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000278   0x08000278   0x00000000   Code   RO         5940    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000278   0x08000278   0x00000000   Code   RO         5942    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000278   0x08000278   0x00000000   Code   RO         5945    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000278   0x08000278   0x00000000   Code   RO         5948    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000278   0x08000278   0x00000000   Code   RO         5950    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000278   0x08000278   0x00000000   Code   RO         5953    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000278   0x08000278   0x00000002   Code   RO         5954    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         5773    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         5794    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x0800027a   0x0800027a   0x00000006   Code   RO         5806    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000280   0x08000280   0x00000000   Code   RO         5796    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000280   0x08000280   0x00000004   Code   RO         5797    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000284   0x08000284   0x00000000   Code   RO         5799    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000284   0x08000284   0x00000008   Code   RO         5800    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800028c   0x0800028c   0x00000002   Code   RO         5835    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800028e   0x0800028e   0x00000000   Code   RO         5889    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800028e   0x0800028e   0x00000004   Code   RO         5890    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000292   0x08000292   0x00000006   Code   RO         5891    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000298   0x08000298   0x00000010   Code   RO         3935    .emb_text           sys.o
    0x080002a8   0x080002a8   0x00000040   Code   RO          532    .text               startup_stm32f407xx.o
    0x080002e8   0x080002e8   0x00000002   Code   RO         5681    .text               c_w.l(use_no_semi_2.o)
    0x080002ea   0x080002ea   0x000000ee   Code   RO         5683    .text               c_w.l(lludivv7m.o)
    0x080003d8   0x080003d8   0x00000018   Code   RO         5687    .text               c_w.l(noretval__2printf.o)
    0x080003f0   0x080003f0   0x0000004e   Code   RO         5691    .text               c_w.l(_printf_pad.o)
    0x0800043e   0x0800043e   0x00000052   Code   RO         5693    .text               c_w.l(_printf_str.o)
    0x08000490   0x08000490   0x00000078   Code   RO         5695    .text               c_w.l(_printf_dec.o)
    0x08000508   0x08000508   0x0000007c   Code   RO         5697    .text               c_w.l(_printf_longlong_dec.o)
    0x08000584   0x08000584   0x00000058   Code   RO         5702    .text               c_w.l(_printf_hex_int.o)
    0x080005dc   0x080005dc   0x00000188   Code   RO         5737    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000764   0x08000764   0x00000048   Code   RO         5747    .text               c_w.l(strcpy.o)
    0x080007ac   0x080007ac   0x00000018   Code   RO         5751    .text               c_w.l(strcat.o)
    0x080007c4   0x080007c4   0x00000006   Code   RO         5755    .text               c_w.l(heapauxi.o)
    0x080007ca   0x080007ca   0x00000002   Code   RO         5771    .text               c_w.l(use_no_semi.o)
    0x080007cc   0x080007cc   0x0000008a   Code   RO         5778    .text               c_w.l(lludiv10.o)
    0x08000856   0x08000856   0x000000b2   Code   RO         5780    .text               c_w.l(_printf_intcommon.o)
    0x08000908   0x08000908   0x0000002c   Code   RO         5782    .text               c_w.l(_printf_char.o)
    0x08000934   0x08000934   0x00000024   Code   RO         5784    .text               c_w.l(_printf_char_file.o)
    0x08000958   0x08000958   0x00000030   Code   RO         5810    .text               c_w.l(_printf_char_common.o)
    0x08000988   0x08000988   0x00000008   Code   RO         5812    .text               c_w.l(ferror.o)
    0x08000990   0x08000990   0x0000004a   Code   RO         5816    .text               c_w.l(sys_stackheap_outer.o)
    0x080009da   0x080009da   0x00000012   Code   RO         5820    .text               c_w.l(exit.o)
    0x080009ec   0x080009ec   0x00000008   Code   RO         5832    .text               c_w.l(libspace.o)
    0x080009f4   0x080009f4   0x00000002   Code   RO          298    i.BusFault_Handler  stm32f4xx_it.o
    0x080009f6   0x080009f6   0x00000002   Code   RO          299    i.DebugMon_Handler  stm32f4xx_it.o
    0x080009f8   0x080009f8   0x00000040   Code   RO         3280    i.FSMC_NORSRAM_Extended_Timing_Init  stm32f4xx_ll_fsmc.o
    0x08000a38   0x08000a38   0x00000054   Code   RO         3281    i.FSMC_NORSRAM_Init  stm32f4xx_ll_fsmc.o
    0x08000a8c   0x08000a8c   0x00000044   Code   RO         3282    i.FSMC_NORSRAM_Timing_Init  stm32f4xx_ll_fsmc.o
    0x08000ad0   0x08000ad0   0x0000008c   Code   RO         5589    i.Get_HzMat         text.o
    0x08000b5c   0x08000b5c   0x00000024   Code   RO         1802    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08000b80   0x08000b80   0x0000001c   Code   RO          546    i.HAL_Delay         stm32f4xx_hal.o
    0x08000b9c   0x08000b9c   0x000001c0   Code   RO          830    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08000d5c   0x08000d5c   0x0000000a   Code   RO          832    i.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x08000d66   0x08000d66   0x0000000a   Code   RO          834    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08000d70   0x08000d70   0x0000000c   Code   RO          551    i.HAL_GetREVID      stm32f4xx_hal.o
    0x08000d7c   0x08000d7c   0x0000000c   Code   RO          552    i.HAL_GetTick       stm32f4xx_hal.o
    0x08000d88   0x08000d88   0x00000010   Code   RO          554    i.HAL_IncTick       stm32f4xx_hal.o
    0x08000d98   0x08000d98   0x0000002c   Code   RO          555    i.HAL_Init          stm32f4xx_hal.o
    0x08000dc4   0x08000dc4   0x00000028   Code   RO          556    i.HAL_InitTick      stm32f4xx_hal.o
    0x08000dec   0x08000dec   0x00000002   Code   RO          503    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08000dee   0x08000dee   0x00000016   Code   RO          697    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08000e04   0x08000e04   0x00000040   Code   RO          703    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08000e44   0x08000e44   0x00000024   Code   RO          704    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08000e68   0x08000e68   0x00000174   Code   RO         1068    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08000fdc   0x08000fdc   0x00000030   Code   RO         1075    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x0800100c   0x0800100c   0x00000030   Code   RO         1076    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x0800103c   0x0800103c   0x00000070   Code   RO         1077    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x080010ac   0x080010ac   0x00000314   Code   RO         1080    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x080013c0   0x080013c0   0x00000078   Code   RO         3401    i.HAL_SD_ConfigWideBusOperation  stm32f4xx_hal_sd.o
    0x08001438   0x08001438   0x000001da   Code   RO         3406    i.HAL_SD_GetCardCSD  stm32f4xx_hal_sd.o
    0x08001612   0x08001612   0x00000024   Code   RO         3407    i.HAL_SD_GetCardInfo  stm32f4xx_hal_sd.o
    0x08001636   0x08001636   0x00000032   Code   RO         3408    i.HAL_SD_GetCardState  stm32f4xx_hal_sd.o
    0x08001668   0x08001668   0x00000036   Code   RO         3413    i.HAL_SD_Init       stm32f4xx_hal_sd.o
    0x0800169e   0x0800169e   0x00000002   PAD
    0x080016a0   0x080016a0   0x00000054   Code   RO         3414    i.HAL_SD_InitCard   stm32f4xx_hal_sd.o
    0x080016f4   0x080016f4   0x000000b8   Code   RO         4505    i.HAL_SD_MspInit    sdio_sdcard.o
    0x080017ac   0x080017ac   0x000001b4   Code   RO         3417    i.HAL_SD_ReadBlocks  stm32f4xx_hal_sd.o
    0x08001960   0x08001960   0x00000186   Code   RO         3422    i.HAL_SD_WriteBlocks  stm32f4xx_hal_sd.o
    0x08001ae6   0x08001ae6   0x0000008c   Code   RO         2826    i.HAL_SPI_Init      stm32f4xx_hal_spi.o
    0x08001b72   0x08001b72   0x00000002   PAD
    0x08001b74   0x08001b74   0x00000050   Code   RO         4340    i.HAL_SPI_MspInit   spi.o
    0x08001bc4   0x08001bc4   0x00000260   Code   RO         2835    i.HAL_SPI_TransmitReceive  stm32f4xx_hal_spi.o
    0x08001e24   0x08001e24   0x00000056   Code   RO         3156    i.HAL_SRAM_Init     stm32f4xx_hal_sram.o
    0x08001e7a   0x08001e7a   0x00000002   PAD
    0x08001e7c   0x08001e7c   0x000000b8   Code   RO         4053    i.HAL_SRAM_MspInit  lcd.o
    0x08001f34   0x08001f34   0x00000018   Code   RO          706    i.HAL_SYSTICK_CLKSourceConfig  stm32f4xx_hal_cortex.o
    0x08001f4c   0x08001f4c   0x00000028   Code   RO          708    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08001f74   0x08001f74   0x00000036   Code   RO         1947    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x08001faa   0x08001faa   0x00000002   Code   RO         1949    i.HAL_TIM_Base_MspInit  stm32f4xx_hal_tim.o
    0x08001fac   0x08001fac   0x00000018   Code   RO         1952    i.HAL_TIM_Base_Start_IT  stm32f4xx_hal_tim.o
    0x08001fc4   0x08001fc4   0x00000002   Code   RO         1232    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08001fc6   0x08001fc6   0x0000000a   Code   RO         1234    i.HAL_UART_GetState  stm32f4xx_hal_uart.o
    0x08001fd0   0x08001fd0   0x00000110   Code   RO         1235    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x080020e0   0x080020e0   0x00000062   Code   RO         1236    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08002142   0x08002142   0x00000002   PAD
    0x08002144   0x08002144   0x0000007c   Code   RO         3963    i.HAL_UART_MspInit  usart.o
    0x080021c0   0x080021c0   0x00000048   Code   RO         1241    i.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08002208   0x08002208   0x00000058   Code   RO         3964    i.HAL_UART_RxCpltCallback  usart.o
    0x08002260   0x08002260   0x00000002   Code   RO         1247    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08002262   0x08002262   0x00000002   PAD
    0x08002264   0x08002264   0x0000001c   Code   RO          300    i.HardFault_Handler  stm32f4xx_it.o
    0x08002280   0x08002280   0x0000005c   Code   RO         4267    i.KEY_Init          key.o
    0x080022dc   0x080022dc   0x00000080   Code   RO         4268    i.KEY_Scan          key.o
    0x0800235c   0x0800235c   0x00000034   Code   RO         4055    i.LCD_Clear         lcd.o
    0x08002390   0x08002390   0x000000e8   Code   RO         4059    i.LCD_Display_Dir   lcd.o
    0x08002478   0x08002478   0x000000d0   Code   RO         4064    i.LCD_Fast_DrawPoint  lcd.o
    0x08002548   0x08002548   0x00000046   Code   RO         4065    i.LCD_Fill          lcd.o
    0x0800258e   0x0800258e   0x00000002   PAD
    0x08002590   0x08002590   0x00002b48   Code   RO         4066    i.LCD_Init          lcd.o
    0x080050d8   0x080050d8   0x00000010   Code   RO         4067    i.LCD_Pow           lcd.o
    0x080050e8   0x080050e8   0x00000014   Code   RO         4068    i.LCD_RD_DATA       lcd.o
    0x080050fc   0x080050fc   0x00000050   Code   RO         4071    i.LCD_SSD_BackLightSet  lcd.o
    0x0800514c   0x0800514c   0x000001a8   Code   RO         4072    i.LCD_Scan_Dir      lcd.o
    0x080052f4   0x080052f4   0x000000f0   Code   RO         4073    i.LCD_SetCursor     lcd.o
    0x080053e4   0x080053e4   0x000000dc   Code   RO         4075    i.LCD_ShowChar      lcd.o
    0x080054c0   0x080054c0   0x00000076   Code   RO         4076    i.LCD_ShowNum       lcd.o
    0x08005536   0x08005536   0x00000054   Code   RO         4077    i.LCD_ShowString    lcd.o
    0x0800558a   0x0800558a   0x00000018   Code   RO         4079    i.LCD_WR_DATA       lcd.o
    0x080055a2   0x080055a2   0x00000018   Code   RO         4080    i.LCD_WR_REG        lcd.o
    0x080055ba   0x080055ba   0x00000002   PAD
    0x080055bc   0x080055bc   0x00000014   Code   RO         4082    i.LCD_WriteRAM_Prepare  lcd.o
    0x080055d0   0x080055d0   0x0000000c   Code   RO         4083    i.LCD_WriteReg      lcd.o
    0x080055dc   0x080055dc   0x00000050   Code   RO         4029    i.LED_Init          led.o
    0x0800562c   0x0800562c   0x00000002   Code   RO          301    i.MemManage_Handler  stm32f4xx_it.o
    0x0800562e   0x0800562e   0x00000002   Code   RO          302    i.NMI_Handler       stm32f4xx_it.o
    0x08005630   0x08005630   0x00000020   Code   RO          710    i.NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08005650   0x08005650   0x00000002   Code   RO          303    i.PendSV_Handler    stm32f4xx_it.o
    0x08005652   0x08005652   0x00000024   Code   RO         3633    i.SDIO_ConfigData   stm32f4xx_ll_sdmmc.o
    0x08005676   0x08005676   0x00000008   Code   RO         3637    i.SDIO_GetPowerState  stm32f4xx_ll_sdmmc.o
    0x0800567e   0x0800567e   0x0000000c   Code   RO         3638    i.SDIO_GetResponse  stm32f4xx_ll_sdmmc.o
    0x0800568a   0x0800568a   0x00000024   Code   RO         3639    i.SDIO_Init         stm32f4xx_ll_sdmmc.o
    0x080056ae   0x080056ae   0x00000008   Code   RO         3641    i.SDIO_PowerState_ON  stm32f4xx_ll_sdmmc.o
    0x080056b6   0x080056b6   0x00000006   Code   RO         3642    i.SDIO_ReadFIFO     stm32f4xx_ll_sdmmc.o
    0x080056bc   0x080056bc   0x00000020   Code   RO         3643    i.SDIO_SendCommand  stm32f4xx_ll_sdmmc.o
    0x080056dc   0x080056dc   0x0000000a   Code   RO         3645    i.SDIO_WriteFIFO    stm32f4xx_ll_sdmmc.o
    0x080056e6   0x080056e6   0x00000030   Code   RO         3646    i.SDMMC_CmdAppCommand  stm32f4xx_ll_sdmmc.o
    0x08005716   0x08005716   0x00000002   PAD
    0x08005718   0x08005718   0x00000034   Code   RO         3647    i.SDMMC_CmdAppOperCommand  stm32f4xx_ll_sdmmc.o
    0x0800574c   0x0800574c   0x00000030   Code   RO         3648    i.SDMMC_CmdBlockLength  stm32f4xx_ll_sdmmc.o
    0x0800577c   0x0800577c   0x00000030   Code   RO         3649    i.SDMMC_CmdBusWidth  stm32f4xx_ll_sdmmc.o
    0x080057ac   0x080057ac   0x00000050   Code   RO         3653    i.SDMMC_CmdGoIdleState  stm32f4xx_ll_sdmmc.o
    0x080057fc   0x080057fc   0x00000068   Code   RO         3655    i.SDMMC_CmdOperCond  stm32f4xx_ll_sdmmc.o
    0x08005864   0x08005864   0x00000030   Code   RO         3656    i.SDMMC_CmdReadMultiBlock  stm32f4xx_ll_sdmmc.o
    0x08005894   0x08005894   0x00000030   Code   RO         3657    i.SDMMC_CmdReadSingleBlock  stm32f4xx_ll_sdmmc.o
    0x080058c4   0x080058c4   0x00000032   Code   RO         3660    i.SDMMC_CmdSelDesel  stm32f4xx_ll_sdmmc.o
    0x080058f6   0x080058f6   0x0000002a   Code   RO         3661    i.SDMMC_CmdSendCID  stm32f4xx_ll_sdmmc.o
    0x08005920   0x08005920   0x0000002c   Code   RO         3662    i.SDMMC_CmdSendCSD  stm32f4xx_ll_sdmmc.o
    0x0800594c   0x0800594c   0x00000030   Code   RO         3663    i.SDMMC_CmdSendSCR  stm32f4xx_ll_sdmmc.o
    0x0800597c   0x0800597c   0x00000030   Code   RO         3664    i.SDMMC_CmdSendStatus  stm32f4xx_ll_sdmmc.o
    0x080059ac   0x080059ac   0x00000030   Code   RO         3665    i.SDMMC_CmdSetRelAdd  stm32f4xx_ll_sdmmc.o
    0x080059dc   0x080059dc   0x00000034   Code   RO         3667    i.SDMMC_CmdStopTransfer  stm32f4xx_ll_sdmmc.o
    0x08005a10   0x08005a10   0x00000030   Code   RO         3669    i.SDMMC_CmdWriteMultiBlock  stm32f4xx_ll_sdmmc.o
    0x08005a40   0x08005a40   0x00000030   Code   RO         3670    i.SDMMC_CmdWriteSingleBlock  stm32f4xx_ll_sdmmc.o
    0x08005a70   0x08005a70   0x0000011c   Code   RO         3671    i.SDMMC_GetCmdResp1  stm32f4xx_ll_sdmmc.o
    0x08005b8c   0x08005b8c   0x00000050   Code   RO         3672    i.SDMMC_GetCmdResp2  stm32f4xx_ll_sdmmc.o
    0x08005bdc   0x08005bdc   0x00000040   Code   RO         3673    i.SDMMC_GetCmdResp3  stm32f4xx_ll_sdmmc.o
    0x08005c1c   0x08005c1c   0x00000084   Code   RO         3674    i.SDMMC_GetCmdResp6  stm32f4xx_ll_sdmmc.o
    0x08005ca0   0x08005ca0   0x000000d6   Code   RO         3430    i.SD_FindSCR        stm32f4xx_hal_sd.o
    0x08005d76   0x08005d76   0x00000002   PAD
    0x08005d78   0x08005d78   0x00000018   Code   RO         4507    i.SD_GetCardState   sdio_sdcard.o
    0x08005d90   0x08005d90   0x00000044   Code   RO         4508    i.SD_Init           sdio_sdcard.o
    0x08005dd4   0x08005dd4   0x000000e6   Code   RO         3431    i.SD_InitCard       stm32f4xx_hal_sd.o
    0x08005eba   0x08005eba   0x000000a2   Code   RO         3432    i.SD_PowerON        stm32f4xx_hal_sd.o
    0x08005f5c   0x08005f5c   0x00000040   Code   RO         4509    i.SD_ReadDisk       sdio_sdcard.o
    0x08005f9c   0x08005f9c   0x0000004e   Code   RO         3434    i.SD_WideBus_Disable  stm32f4xx_hal_sd.o
    0x08005fea   0x08005fea   0x0000004e   Code   RO         3435    i.SD_WideBus_Enable  stm32f4xx_hal_sd.o
    0x08006038   0x08006038   0x00000040   Code   RO         4510    i.SD_WriteDisk      sdio_sdcard.o
    0x08006078   0x08006078   0x00000050   Code   RO         4341    i.SPI1_Init         spi.o
    0x080060c8   0x080060c8   0x00000020   Code   RO         4342    i.SPI1_ReadWriteByte  spi.o
    0x080060e8   0x080060e8   0x00000034   Code   RO         4343    i.SPI1_SetSpeed     spi.o
    0x0800611c   0x0800611c   0x00000020   Code   RO         2852    i.SPI_CheckFlag_BSY  stm32f4xx_hal_spi.o
    0x0800613c   0x0800613c   0x00000096   Code   RO         2872    i.SPI_WaitFlagStateUntilTimeout  stm32f4xx_hal_spi.o
    0x080061d2   0x080061d2   0x00000002   PAD
    0x080061d4   0x080061d4   0x00000108   Code   RO         4300    i.SRAM_Init         sram.o
    0x080062dc   0x080062dc   0x00000002   Code   RO          304    i.SVC_Handler       stm32f4xx_it.o
    0x080062de   0x080062de   0x00000002   PAD
    0x080062e0   0x080062e0   0x000000a4   Code   RO         5590    i.Show_Font         text.o
    0x08006384   0x08006384   0x000000ae   Code   RO         5591    i.Show_Str          text.o
    0x08006432   0x08006432   0x00000002   PAD
    0x08006434   0x08006434   0x00000094   Code   RO         3936    i.Stm32_Clock_Init  sys.o
    0x080064c8   0x080064c8   0x00000004   Code   RO          305    i.SysTick_Handler   stm32f4xx_it.o
    0x080064cc   0x080064cc   0x00000054   Code   RO          377    i.SystemInit        system_stm32f4xx.o
    0x08006520   0x08006520   0x00000034   Code   RO         4568    i.TIM4_IRQHandler   usmart.o
    0x08006554   0x08006554   0x000000c0   Code   RO         2031    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08006614   0x08006614   0x00000054   Code   RO         4569    i.Timer4_Init       usmart.o
    0x08006668   0x08006668   0x00000010   Code   RO         1249    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08006678   0x08006678   0x0000001c   Code   RO         1259    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08006694   0x08006694   0x0000008c   Code   RO         1261    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x08006720   0x08006720   0x000002ac   Code   RO         1262    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x080069cc   0x080069cc   0x00000062   Code   RO         1263    i.UART_Transmit_IT  stm32f4xx_hal_uart.o
    0x08006a2e   0x08006a2e   0x00000002   PAD
    0x08006a30   0x08006a30   0x0000002c   Code   RO         3965    i.USART1_IRQHandler  usart.o
    0x08006a5c   0x08006a5c   0x00000002   Code   RO          306    i.UsageFault_Handler  stm32f4xx_it.o
    0x08006a5e   0x08006a5e   0x00000002   PAD
    0x08006a60   0x08006a60   0x0000002c   Code   RO         4388    i.W25QXX_Erase_Chip  w25qxx.o
    0x08006a8c   0x08006a8c   0x00000058   Code   RO         4389    i.W25QXX_Erase_Sector  w25qxx.o
    0x08006ae4   0x08006ae4   0x0000007c   Code   RO         4390    i.W25QXX_Init       w25qxx.o
    0x08006b60   0x08006b60   0x00000068   Code   RO         4392    i.W25QXX_Read       w25qxx.o
    0x08006bc8   0x08006bc8   0x00000044   Code   RO         4393    i.W25QXX_ReadID     w25qxx.o
    0x08006c0c   0x08006c0c   0x00000038   Code   RO         4394    i.W25QXX_ReadSR     w25qxx.o
    0x08006c44   0x08006c44   0x0000000e   Code   RO         4396    i.W25QXX_Wait_Busy  w25qxx.o
    0x08006c52   0x08006c52   0x00000002   PAD
    0x08006c54   0x08006c54   0x000000b0   Code   RO         4397    i.W25QXX_Write      w25qxx.o
    0x08006d04   0x08006d04   0x00000020   Code   RO         4399    i.W25QXX_Write_Enable  w25qxx.o
    0x08006d24   0x08006d24   0x0000003e   Code   RO         4400    i.W25QXX_Write_NoCheck  w25qxx.o
    0x08006d62   0x08006d62   0x00000002   PAD
    0x08006d64   0x08006d64   0x0000006c   Code   RO         4401    i.W25QXX_Write_Page  w25qxx.o
    0x08006dd0   0x08006dd0   0x0000000e   Code   RO         5730    i._is_digit         c_w.l(__printf_wp.o)
    0x08006dde   0x08006dde   0x00000002   Code   RO         3966    i._sys_exit         usart.o
    0x08006de0   0x08006de0   0x00000076   Code   RO         4845    i.change_bitmap     ff.o
    0x08006e56   0x08006e56   0x00000002   PAD
    0x08006e58   0x08006e58   0x00000074   Code   RO         4846    i.check_fs          ff.o
    0x08006ecc   0x08006ecc   0x00000010   Code   RO         4847    i.chk_chr           ff.o
    0x08006edc   0x08006edc   0x00000032   Code   RO         4848    i.clmt_clust        ff.o
    0x08006f0e   0x08006f0e   0x00000018   Code   RO         4849    i.clust2sect        ff.o
    0x08006f26   0x08006f26   0x00000002   PAD
    0x08006f28   0x08006f28   0x00000084   Code   RO         4850    i.cmp_lfn           ff.o
    0x08006fac   0x08006fac   0x0000010e   Code   RO         4851    i.create_chain      ff.o
    0x080070ba   0x080070ba   0x00000002   PAD
    0x080070bc   0x080070bc   0x00000218   Code   RO         4852    i.create_name       ff.o
    0x080072d4   0x080072d4   0x0000007c   Code   RO         4853    i.create_xdir       ff.o
    0x08007350   0x08007350   0x00000014   Code   RO         3897    i.delay_init        delay.o
    0x08007364   0x08007364   0x0000001a   Code   RO         3898    i.delay_ms          delay.o
    0x0800737e   0x0800737e   0x00000002   PAD
    0x08007380   0x08007380   0x00000034   Code   RO         3899    i.delay_us          delay.o
    0x080073b4   0x080073b4   0x00000058   Code   RO         4854    i.dir_alloc         ff.o
    0x0800740c   0x0800740c   0x0000013c   Code   RO         4855    i.dir_find          ff.o
    0x08007548   0x08007548   0x00000104   Code   RO         4856    i.dir_next          ff.o
    0x0800764c   0x0800764c   0x000000e6   Code   RO         4857    i.dir_read          ff.o
    0x08007732   0x08007732   0x00000002   PAD
    0x08007734   0x08007734   0x0000023c   Code   RO         4858    i.dir_register      ff.o
    0x08007970   0x08007970   0x0000005e   Code   RO         4859    i.dir_remove        ff.o
    0x080079ce   0x080079ce   0x00000096   Code   RO         4860    i.dir_sdi           ff.o
    0x08007a64   0x08007a64   0x0000001c   Code   RO         5254    i.disk_initialize   diskio.o
    0x08007a80   0x08007a80   0x00000060   Code   RO         5255    i.disk_ioctl        diskio.o
    0x08007ae0   0x08007ae0   0x00000052   Code   RO         5256    i.disk_read         diskio.o
    0x08007b32   0x08007b32   0x00000004   Code   RO         5257    i.disk_status       diskio.o
    0x08007b36   0x08007b36   0x00000052   Code   RO         5258    i.disk_write        diskio.o
    0x08007b88   0x08007b88   0x00000064   Code   RO         5329    i.exfuns_init       exfuns.o
    0x08007bec   0x08007bec   0x0000001e   Code   RO         4861    i.f_close           ff.o
    0x08007c0a   0x08007c0a   0x00000014   Code   RO         4862    i.f_closedir        ff.o
    0x08007c1e   0x08007c1e   0x00000118   Code   RO         4863    i.f_getfree         ff.o
    0x08007d36   0x08007d36   0x000000f2   Code   RO         4864    i.f_getlabel        ff.o
    0x08007e28   0x08007e28   0x00000044   Code   RO         4865    i.f_gets            ff.o
    0x08007e6c   0x08007e6c   0x000002bc   Code   RO         4866    i.f_lseek           ff.o
    0x08008128   0x08008128   0x000001dc   Code   RO         4867    i.f_mkdir           ff.o
    0x08008304   0x08008304   0x000004c4   Code   RO         4868    i.f_mkfs            ff.o
    0x080087c8   0x080087c8   0x0000004c   Code   RO         4869    i.f_mount           ff.o
    0x08008814   0x08008814   0x00000276   Code   RO         4870    i.f_open            ff.o
    0x08008a8a   0x08008a8a   0x000000d4   Code   RO         4871    i.f_opendir         ff.o
    0x08008b5e   0x08008b5e   0x0000003c   Code   RO         4873    i.f_putc            ff.o
    0x08008b9a   0x08008b9a   0x00000046   Code   RO         4874    i.f_puts            ff.o
    0x08008be0   0x08008be0   0x00000176   Code   RO         4875    i.f_read            ff.o
    0x08008d56   0x08008d56   0x00000076   Code   RO         4876    i.f_readdir         ff.o
    0x08008dcc   0x08008dcc   0x0000018e   Code   RO         4877    i.f_rename          ff.o
    0x08008f5a   0x08008f5a   0x00000002   PAD
    0x08008f5c   0x08008f5c   0x000001d0   Code   RO         4878    i.f_setlabel        ff.o
    0x0800912c   0x0800912c   0x00000152   Code   RO         4880    i.f_sync            ff.o
    0x0800927e   0x0800927e   0x0000010a   Code   RO         4882    i.f_unlink          ff.o
    0x08009388   0x08009388   0x000001d4   Code   RO         4883    i.f_write           ff.o
    0x0800955c   0x0800955c   0x00000070   Code   RO         5558    i.ff_convert        mycc936.o
    0x080095cc   0x080095cc   0x00000008   Code   RO         5259    i.ff_memalloc       diskio.o
    0x080095d4   0x080095d4   0x00000008   Code   RO         5260    i.ff_memfree        diskio.o
    0x080095dc   0x080095dc   0x00000024   Code   RO         5559    i.ff_wtoupper       mycc936.o
    0x08009600   0x08009600   0x0000002e   Code   RO         4884    i.fill_fat_chain    ff.o
    0x0800962e   0x0800962e   0x00000092   Code   RO         4885    i.find_bitmap       ff.o
    0x080096c0   0x080096c0   0x00000350   Code   RO         4886    i.find_volume       ff.o
    0x08009a10   0x08009a10   0x000000b4   Code   RO         4887    i.follow_path       ff.o
    0x08009ac4   0x08009ac4   0x00000040   Code   RO         5631    i.font_init         fontupd.o
    0x08009b04   0x08009b04   0x00000014   Code   RO         3967    i.fputc             usart.o
    0x08009b18   0x08009b18   0x00000084   Code   RO         5632    i.fupd_prog         fontupd.o
    0x08009b9c   0x08009b9c   0x0000008c   Code   RO         4888    i.gen_numname       ff.o
    0x08009c28   0x08009c28   0x00000126   Code   RO         4889    i.get_fat           ff.o
    0x08009d4e   0x08009d4e   0x00000004   Code   RO         5261    i.get_fattime       diskio.o
    0x08009d52   0x08009d52   0x00000106   Code   RO         4890    i.get_fileinfo      ff.o
    0x08009e58   0x08009e58   0x0000003c   Code   RO         4891    i.get_ldnumber      ff.o
    0x08009e94   0x08009e94   0x0000008a   Code   RO         4892    i.get_xdir_info     ff.o
    0x08009f1e   0x08009f1e   0x00000026   Code   RO         4893    i.ld_clust          ff.o
    0x08009f44   0x08009f44   0x00000016   Code   RO         4894    i.ld_dword          ff.o
    0x08009f5a   0x08009f5a   0x00000056   Code   RO         4895    i.ld_qword          ff.o
    0x08009fb0   0x08009fb0   0x0000000a   Code   RO         4896    i.ld_word           ff.o
    0x08009fba   0x08009fba   0x00000038   Code   RO         4897    i.load_obj_dir      ff.o
    0x08009ff2   0x08009ff2   0x000000aa   Code   RO         4898    i.load_xdir         ff.o
    0x0800a09c   0x0800a09c   0x0000044c   Code   RO            4    i.main              main.o
    0x0800a4e8   0x0800a4e8   0x00000018   Code   RO         4899    i.mem_cmp           ff.o
    0x0800a500   0x0800a500   0x00000012   Code   RO         4900    i.mem_cpy           ff.o
    0x0800a512   0x0800a512   0x0000000c   Code   RO         4901    i.mem_set           ff.o
    0x0800a51e   0x0800a51e   0x00000002   PAD
    0x0800a520   0x0800a520   0x00000014   Code   RO         5408    i.mf_close          fattester.o
    0x0800a534   0x0800a534   0x0000000c   Code   RO         5409    i.mf_closedir       fattester.o
    0x0800a540   0x0800a540   0x00000004   Code   RO         5410    i.mf_fmkfs          fattester.o
    0x0800a544   0x0800a544   0x00000080   Code   RO         5411    i.mf_getlabel       fattester.o
    0x0800a5c4   0x0800a5c4   0x00000048   Code   RO         5412    i.mf_gets           fattester.o
    0x0800a60c   0x0800a60c   0x00000010   Code   RO         5413    i.mf_lseek          fattester.o
    0x0800a61c   0x0800a61c   0x00000004   Code   RO         5414    i.mf_mkdir          fattester.o
    0x0800a620   0x0800a620   0x00000014   Code   RO         5415    i.mf_mount          fattester.o
    0x0800a634   0x0800a634   0x0000002c   Code   RO         5416    i.mf_open           fattester.o
    0x0800a660   0x0800a660   0x0000000c   Code   RO         5417    i.mf_opendir        fattester.o
    0x0800a66c   0x0800a66c   0x00000014   Code   RO         5418    i.mf_putc           fattester.o
    0x0800a680   0x0800a680   0x00000014   Code   RO         5419    i.mf_puts           fattester.o
    0x0800a694   0x0800a694   0x0000014c   Code   RO         5420    i.mf_read           fattester.o
    0x0800a7e0   0x0800a7e0   0x000001a8   Code   RO         5421    i.mf_readdir        fattester.o
    0x0800a988   0x0800a988   0x00000004   Code   RO         5422    i.mf_rename         fattester.o
    0x0800a98c   0x0800a98c   0x0000005c   Code   RO         5423    i.mf_scan_files     fattester.o
    0x0800a9e8   0x0800a9e8   0x00000054   Code   RO         5424    i.mf_setlabel       fattester.o
    0x0800aa3c   0x0800aa3c   0x00000090   Code   RO         5425    i.mf_showfree       fattester.o
    0x0800aacc   0x0800aacc   0x0000000c   Code   RO         5426    i.mf_size           fattester.o
    0x0800aad8   0x0800aad8   0x0000000c   Code   RO         5427    i.mf_tell           fattester.o
    0x0800aae4   0x0800aae4   0x00000004   Code   RO         5428    i.mf_unlink         fattester.o
    0x0800aae8   0x0800aae8   0x000000b4   Code   RO         5429    i.mf_write          fattester.o
    0x0800ab9c   0x0800ab9c   0x00000032   Code   RO         4902    i.move_window       ff.o
    0x0800abce   0x0800abce   0x00000002   PAD
    0x0800abd0   0x0800abd0   0x00000058   Code   RO         4765    i.my_mem_free       malloc.o
    0x0800ac28   0x0800ac28   0x0000003c   Code   RO         4766    i.my_mem_init       malloc.o
    0x0800ac64   0x0800ac64   0x0000008c   Code   RO         4767    i.my_mem_malloc     malloc.o
    0x0800acf0   0x0800acf0   0x00000038   Code   RO         4768    i.my_mem_perused    malloc.o
    0x0800ad28   0x0800ad28   0x00000018   Code   RO         4769    i.myfree            malloc.o
    0x0800ad40   0x0800ad40   0x00000020   Code   RO         4770    i.mymalloc          malloc.o
    0x0800ad60   0x0800ad60   0x0000000c   Code   RO         4772    i.mymemset          malloc.o
    0x0800ad6c   0x0800ad6c   0x00000070   Code   RO         4903    i.pick_lfn          ff.o
    0x0800addc   0x0800addc   0x000000f6   Code   RO         4904    i.put_fat           ff.o
    0x0800aed2   0x0800aed2   0x0000003a   Code   RO         4905    i.putc_bfd          ff.o
    0x0800af0c   0x0800af0c   0x00000004   Code   RO         4570    i.read_addr         usmart.o
    0x0800af10   0x0800af10   0x000000e8   Code   RO         4906    i.remove_chain      ff.o
    0x0800aff8   0x0800aff8   0x00000028   Code   RO         4907    i.st_clust          ff.o
    0x0800b020   0x0800b020   0x00000010   Code   RO         4908    i.st_dword          ff.o
    0x0800b030   0x0800b030   0x0000004a   Code   RO         4909    i.st_qword          ff.o
    0x0800b07a   0x0800b07a   0x00000008   Code   RO         4910    i.st_word           ff.o
    0x0800b082   0x0800b082   0x0000006c   Code   RO         4911    i.store_xdir        ff.o
    0x0800b0ee   0x0800b0ee   0x0000001a   Code   RO         4912    i.sum_sfn           ff.o
    0x0800b108   0x0800b108   0x00000088   Code   RO         4913    i.sync_fs           ff.o
    0x0800b190   0x0800b190   0x00000052   Code   RO         4914    i.sync_window       ff.o
    0x0800b1e2   0x0800b1e2   0x00000002   PAD
    0x0800b1e4   0x0800b1e4   0x00000038   Code   RO         3968    i.uart_init         usart.o
    0x0800b21c   0x0800b21c   0x000000f0   Code   RO         5633    i.updata_fontx      fontupd.o
    0x0800b30c   0x0800b30c   0x00000310   Code   RO         5634    i.update_font       fontupd.o
    0x0800b61c   0x0800b61c   0x00000094   Code   RO         4571    i.usmart_cmd_rec    usmart.o
    0x0800b6b0   0x0800b6b0   0x00000260   Code   RO         4572    i.usmart_exe        usmart.o
    0x0800b910   0x0800b910   0x000000a4   Code   RO         4657    i.usmart_get_aparm  usmart_str.o
    0x0800b9b4   0x0800b9b4   0x0000002e   Code   RO         4658    i.usmart_get_cmdname  usmart_str.o
    0x0800b9e2   0x0800b9e2   0x00000002   PAD
    0x0800b9e4   0x0800b9e4   0x00000168   Code   RO         4659    i.usmart_get_fname  usmart_str.o
    0x0800bb4c   0x0800bb4c   0x000000d0   Code   RO         4660    i.usmart_get_fparam  usmart_str.o
    0x0800bc1c   0x0800bc1c   0x00000024   Code   RO         4661    i.usmart_get_parmpos  usmart_str.o
    0x0800bc40   0x0800bc40   0x00000030   Code   RO         4573    i.usmart_get_runtime  usmart.o
    0x0800bc70   0x0800bc70   0x00000028   Code   RO         4574    i.usmart_init       usmart.o
    0x0800bc98   0x0800bc98   0x00000010   Code   RO         4662    i.usmart_pow        usmart_str.o
    0x0800bca8   0x0800bca8   0x00000028   Code   RO         4575    i.usmart_reset_runtime  usmart.o
    0x0800bcd0   0x0800bcd0   0x000000a4   Code   RO         4576    i.usmart_scan       usmart.o
    0x0800bd74   0x0800bd74   0x00000010   Code   RO         4663    i.usmart_search_nextc  usmart_str.o
    0x0800bd84   0x0800bd84   0x000000d6   Code   RO         4664    i.usmart_str2num    usmart_str.o
    0x0800be5a   0x0800be5a   0x00000018   Code   RO         4665    i.usmart_strcmp     usmart_str.o
    0x0800be72   0x0800be72   0x00000012   Code   RO         4666    i.usmart_strcopy    usmart_str.o
    0x0800be84   0x0800be84   0x00000012   Code   RO         4667    i.usmart_strlen     usmart_str.o
    0x0800be96   0x0800be96   0x00000002   PAD
    0x0800be98   0x0800be98   0x00000680   Code   RO         4577    i.usmart_sys_cmd_exe  usmart.o
    0x0800c518   0x0800c518   0x00000032   Code   RO         4915    i.validate          ff.o
    0x0800c54a   0x0800c54a   0x00000004   Code   RO         4578    i.write_addr        usmart.o
    0x0800c54e   0x0800c54e   0x00000034   Code   RO         4916    i.xdir_sum          ff.o
    0x0800c582   0x0800c582   0x0000003c   Code   RO         4917    i.xname_sum         ff.o
    0x0800c5be   0x0800c5be   0x00000002   PAD
    0x0800c5c0   0x0800c5c0   0x0000005a   Code   RO         5759    x$fpl$dfixu         fz_wm.l(dfixu.o)
    0x0800c61a   0x0800c61a   0x00000026   Code   RO         5763    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x0800c640   0x0800c640   0x00000154   Code   RO         5769    x$fpl$dmul          fz_wm.l(dmul.o)
    0x0800c794   0x0800c794   0x0000009c   Code   RO         5788    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x0800c830   0x0800c830   0x0000000c   Code   RO         5790    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x0800c83c   0x0800c83c   0x0000000a   Code   RO         5901    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x0800c846   0x0800c846   0x00000000   Code   RO         5792    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x0800c846   0x0800c846   0x00000010   Data   RO          378    .constdata          system_stm32f4xx.o
    0x0800c856   0x0800c856   0x00000008   Data   RO          379    .constdata          system_stm32f4xx.o
    0x0800c85e   0x0800c85e   0x00004740   Data   RO         4086    .constdata          lcd.o
    0x08010f9e   0x08010f9e   0x00000002   PAD
    0x08010fa0   0x08010fa0   0x00000024   Data   RO         4780    .constdata          malloc.o
    0x08010fc4   0x08010fc4   0x0000004a   Data   RO         4918    .constdata          ff.o
    0x0801100e   0x0801100e   0x000003c0   Data   RO         5560    .constdata          mycc936.o
    0x080113ce   0x080113ce   0x00000002   PAD
    0x080113d0   0x080113d0   0x00000010   Data   RO         5636    .constdata          fontupd.o
    0x080113e0   0x080113e0   0x00000028   Data   RO         5703    .constdata          c_w.l(_printf_hex_int.o)
    0x08011408   0x08011408   0x00000011   Data   RO         5738    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x08011419   0x08011419   0x00000003   PAD
    0x0801141c   0x0801141c   0x00000028   Data   RO         4580    .conststring        usmart.o
    0x08011444   0x08011444   0x00000289   Data   RO         4736    .conststring        usmart_config.o
    0x080116cd   0x080116cd   0x00000003   PAD
    0x080116d0   0x080116d0   0x0000005f   Data   RO         5637    .conststring        fontupd.o
    0x0801172f   0x0801172f   0x00000001   PAD
    0x08011730   0x08011730   0x00000020   Data   RO         5955    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08011750, Size: 0x0001c630, Max: 0x00020000, ABSOLUTE, COMPRESSED[0x000000f8])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000004   Data   RW          380    .data               system_stm32f4xx.o
    0x20000004   COMPRESSED   0x00000004   Data   RW          561    .data               stm32f4xx_hal.o
    0x20000008   COMPRESSED   0x00000004   Data   RW         3900    .data               delay.o
    0x2000000c   COMPRESSED   0x00000004   Data   RW         3970    .data               usart.o
    0x20000010   COMPRESSED   0x00000004   Data   RW         3971    .data               usart.o
    0x20000014   COMPRESSED   0x00000008   Data   RW         4087    .data               lcd.o
    0x2000001c   COMPRESSED   0x00000001   Data   RW         4269    .data               key.o
    0x2000001d   COMPRESSED   0x00000001   PAD
    0x2000001e   COMPRESSED   0x00000002   Data   RW         4404    .data               w25qxx.o
    0x20000020   COMPRESSED   0x0000001c   Data   RW         4581    .data               usmart.o
    0x2000003c   COMPRESSED   0x000000c8   Data   RW         4737    .data               usmart_config.o
    0x20000104   COMPRESSED   0x000000f4   Data   RW         4738    .data               usmart_config.o
    0x200001f8   COMPRESSED   0x00000024   Data   RW         4781    .data               malloc.o
    0x2000021c   COMPRESSED   0x0000000c   Data   RW         4919    .data               ff.o
    0x20000228   COMPRESSED   0x00000002   Data   RW         5262    .data               diskio.o
    0x2000022a   COMPRESSED   0x00000002   PAD
    0x2000022c   COMPRESSED   0x00000014   Data   RW         5335    .data               exfuns.o
    0x20000240   COMPRESSED   0x00000004   Data   RW         5336    .data               exfuns.o
    0x20000244   COMPRESSED   0x00000004   Data   RW         5337    .data               exfuns.o
    0x20000248        -       0x00000108   Zero   RW         3969    .bss                usart.o
    0x20000350        -       0x0000005e   Zero   RW         4085    .bss                lcd.o
    0x200003ae   COMPRESSED   0x00000002   PAD
    0x200003b0        -       0x00000050   Zero   RW         4301    .bss                sram.o
    0x20000400        -       0x00000058   Zero   RW         4344    .bss                spi.o
    0x20000458        -       0x00001000   Zero   RW         4403    .bss                w25qxx.o
    0x20001458        -       0x000000a4   Zero   RW         4511    .bss                sdio_sdcard.o
    0x200014fc        -       0x0000003c   Zero   RW         4579    .bss                usmart.o
    0x20001538   COMPRESSED   0x00000008   PAD
    0x20001540        -       0x00019000   Zero   RW         4778    .bss                malloc.o
    0x2001a540        -       0x00001900   Zero   RW         4779    .bss                malloc.o
    0x2001be40        -       0x00000120   Zero   RW         5331    .bss                exfuns.o
    0x2001bf60        -       0x00000048   Zero   RW         5332    .bss                exfuns.o
    0x2001bfa8        -       0x00000021   Zero   RW         5635    .bss                fontupd.o
    0x2001bfc9   COMPRESSED   0x00000003   PAD
    0x2001bfcc        -       0x00000060   Zero   RW         5833    .bss                c_w.l(libspace.o)
    0x2001c02c   COMPRESSED   0x00000004   PAD
    0x2001c030        -       0x00000200   Zero   RW          530    HEAP                startup_stm32f407xx.o
    0x2001c230        -       0x00000400   Zero   RW          529    STACK               startup_stm32f407xx.o



  Load Region LR$$.ARM.__AT_0x10000000 (Base: 0x10000000, Size: 0x00000000, Max: 0x0000f000, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x10000000 (Exec base: 0x10000000, Load base: 0x10000000, Size: 0x0000f000, Max: 0x0000f000, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x10000000        -       0x0000f000   Zero   RW         4774    .ARM.__AT_0x10000000  malloc.o



  Load Region LR$$.ARM.__AT_0x1000F000 (Base: 0x1000f000, Size: 0x00000000, Max: 0x00000f00, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x1000F000 (Exec base: 0x1000f000, Load base: 0x1000f000, Size: 0x00000f00, Max: 0x00000f00, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x1000f000        -       0x00000f00   Zero   RW         4775    .ARM.__AT_0x1000F000  malloc.o



  Load Region LR$$.ARM.__AT_0x68000000 (Base: 0x68000000, Size: 0x00000000, Max: 0x000f0000, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x68000000 (Exec base: 0x68000000, Load base: 0x68000000, Size: 0x000f0000, Max: 0x000f0000, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x68000000        -       0x000f0000   Zero   RW         4776    .ARM.__AT_0x68000000  malloc.o



  Load Region LR$$.ARM.__AT_0x680F0000 (Base: 0x680f0000, Size: 0x00000000, Max: 0x0000f000, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x680F0000 (Exec base: 0x680f0000, Load base: 0x680f0000, Size: 0x0000f000, Max: 0x0000f000, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x680f0000        -       0x0000f000   Zero   RW         4777    .ARM.__AT_0x680F0000  malloc.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        98          8          0          4          0       2107   delay.o
       312         10          0          2          0       6048   diskio.o
       100          6          0         28        360       1899   exfuns.o
      1660        844          0          0          0      14450   fattester.o
     13506        164         74         12          0      74962   ff.o
      1220        146        111          0         33       6330   fontupd.o
       220         24          0          1          0       1750   key.o
     13108        142      18240          8         94      22827   lcd.o
        80          8          0          0          0        955   led.o
      1100        316          0          0          0     721355   main.o
       412         40         36         36    1218560       6977   malloc.o
       148         14        960          0          0       2075   mycc936.o
       404         48          0          0        164       4801   sdio_sdcard.o
       244         26          0          0         88       3098   spi.o
       264         28          0          0         80       1519   sram.o
        64         26        392          0       1536        868   startup_stm32f407xx.o
       152         26          0          4          0       5302   stm32f4xx_hal.o
       218         14          0          0          0      31181   stm32f4xx_hal_cortex.o
        36          0          0          0          0       1299   stm32f4xx_hal_dma.o
       468         46          0          0          0       3367   stm32f4xx_hal_gpio.o
         2          0          0          0          0        486   stm32f4xx_hal_msp.o
      1368         74          0          0          0       5568   stm32f4xx_hal_rcc.o
      2406          4          0          0          0      13924   stm32f4xx_hal_sd.o
       930          0          0          0          0       4769   stm32f4xx_hal_spi.o
        86          0          0          0          0       1300   stm32f4xx_hal_sram.o
       272         44          0          0          0       4172   stm32f4xx_hal_tim.o
      1422         14          0          0          0       8887   stm32f4xx_hal_uart.o
        46         20          0          0          0       4366   stm32f4xx_it.o
       216         12          0          0          0       2875   stm32f4xx_ll_fsmc.o
      1612         44          0          0          0      23971   stm32f4xx_ll_sdmmc.o
       164         12          0          0          0       1755   sys.o
        84         18         24          4          0       1207   system_stm32f4xx.o
       478         12          0          0          0       3703   text.o
       334         52          0          8        264       5721   usart.o
      2856       1354         40         28         60       8515   usmart.o
         0          0        649        444          0       1692   usmart_config.o
      1120         20          0          0          0      10035   usmart_str.o
       876         84          0          2       4096       8697   w25qxx.o

    ----------------------------------------------------------------------
     48142       <USER>      <GROUP>        584    1225348    1024813   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        56          0          8          3         13          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
        88          4         40          0          0         88   _printf_hex_int.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
       124         16          0          0          0         92   _printf_longlong_dec.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
         6          0          0          0          0          0   _printf_u.o
         6          0          0          0          0          0   _printf_x.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
       238          0          0          0          0        100   lludivv7m.o
        24          4          0          0          0         84   noretval__2printf.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        24          0          0          0          0         68   strcat.o
        72          0          0          0          0         80   strcpy.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        90          4          0          0          0        140   dfixu.o
        38          0          0          0          0        116   dflt_clz.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
        10          0          0          0          0        116   fpinit.o
         0          0          0          0          0          0   usenofp.o

    ----------------------------------------------------------------------
      2736         <USER>         <GROUP>          0        100       2836   Library Totals
         2          0          3          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2088         66         57          0         96       2056   c_w.l
       646         20          0          0          0        780   fz_wm.l

    ----------------------------------------------------------------------
      2736         <USER>         <GROUP>          0        100       2836   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     50878       3786      20626        584    1225448     998009   Grand Totals
     50878       3786      20626        248    1225448     998009   ELF Image Totals (compressed)
     50878       3786      20626        248          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                71504 (  69.83kB)
    Total RW  Size (RW Data + ZI Data)           1226032 (1197.30kB)
    Total ROM Size (Code + RO Data + RW Data)      71752 (  70.07kB)

==============================================================================

