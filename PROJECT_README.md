# STM32F407ZGT6 超声波测距系统

## 项目概述

这是一个基于STM32F407ZGT6微控制器的多功能超声波测距系统，专为2025年大学生电子设计竞赛设计。系统采用状态机架构，集成了超声波测距、ADC数据采集、LCD显示和UART调试等功能。

## 主要特性

- **状态机管理**: 采用有限状态机管理系统运行状态
- **超声波测距**: HC-SR04传感器，测量范围2-400cm
- **多通道ADC**: 4通道12位ADC数据采集
- **LCD显示**: 实时显示测量数据和系统状态
- **UART调试**: 扩展的UART功能，支持多级别调试输出
- **按键交互**: 支持按键控制和模式切换
- **图形显示**: 支持数据图形化显示

## 硬件连接

### 超声波传感器 (HC-SR04)
- TRIG: PE2
- ECHO: PE3
- VCC: 5V
- GND: GND

### ADC通道
- CH0: PA0
- CH1: PA1
- CH2: PA2
- CH3: PA3

### LED指示灯
- LED0: PF9 (系统运行指示)
- LED1: PF10 (状态指示)

### 按键
- KEY0: PE4 (开始/停止测量)
- KEY1: PE3 (切换显示模式)
- KEY_UP: PA0 (系统重置)

### LCD显示屏
- 通过FSMC接口连接
- 支持中文显示

## 软件架构

### 模块结构
```
├── HARDWARE/
│   ├── STATE_MACHINE/     # 状态机模块
│   ├── ULTRASONIC/        # 超声波驱动
│   ├── ADC/               # ADC驱动
│   ├── UART_EXT/          # UART扩展功能
│   └── DISPLAY/           # 显示管理模块
├── SYSTEM/                # 系统基础模块
├── USER/                  # 用户应用代码
└── HALLIB/                # HAL库文件
```

### 状态机设计
- **IDLE**: 空闲状态
- **INIT**: 初始化状态
- **RUNNING**: 运行状态
- **MEASURING**: 测量状态
- **DISPLAYING**: 显示状态
- **ERROR**: 错误状态
- **SLEEP**: 睡眠状态

## 编译说明

### 开发环境
- Keil MDK-ARM 5.x
- STM32CubeMX (可选)
- STM32F4xx HAL库

### 编译步骤
1. 打开 `USER/HZ.uvprojx` 项目文件
2. 确保所有源文件已添加到项目中
3. 配置编译器选项
4. 编译生成HEX文件
5. 下载到STM32F407ZGT6开发板

### 需要添加的源文件
```c
// 新增模块源文件
HARDWARE/STATE_MACHINE/state_machine.c
HARDWARE/ULTRASONIC/ultrasonic.c
HARDWARE/ADC/adc.c
HARDWARE/UART_EXT/uart_ext.c
HARDWARE/DISPLAY/display_manager.c
USER/system_test.c
```

### 需要添加的头文件路径
```
HARDWARE/STATE_MACHINE
HARDWARE/ULTRASONIC
HARDWARE/ADC
HARDWARE/UART_EXT
HARDWARE/DISPLAY
```

## 使用说明

### 系统启动
1. 上电后系统自动初始化
2. LCD显示系统信息
3. UART输出调试信息

### 操作方式
- **KEY0**: 开始/停止测量
- **KEY1**: 切换显示模式 (主界面/调试/设置/图形)
- **KEY_UP**: 系统重置

### 显示模式
1. **主界面**: 显示超声波距离、ADC数据、系统状态
2. **调试模式**: 显示系统调试信息
3. **设置界面**: 系统参数设置
4. **图形模式**: 数据图形化显示

### UART调试
- 波特率: 115200
- 数据位: 8
- 停止位: 1
- 校验位: 无

调试命令:
- `help`: 显示帮助信息
- `info`: 显示系统信息
- `debug <level>`: 设置调试级别 (0-5)
- `clear`: 清屏
- `reset`: 系统重置

## 测试功能

系统包含完整的测试套件，可以验证各个模块的功能:

```c
#include "system_test.h"

// 在main函数中调用
SystemTest_RunAll();
```

测试项目包括:
- 基础硬件测试
- 内存系统测试
- 时序系统测试
- 状态机测试
- 超声波传感器测试
- ADC模块测试
- UART扩展功能测试
- 显示管理器测试
- 系统集成测试

## 性能参数

- **测量精度**: ±1cm
- **测量范围**: 2-400cm
- **测量频率**: 最高10Hz
- **ADC分辨率**: 12位
- **显示刷新率**: 2Hz
- **系统响应时间**: <100ms

## 故障排除

### 常见问题
1. **超声波无读数**: 检查传感器连接和供电
2. **LCD无显示**: 检查FSMC接口连接
3. **UART无输出**: 检查串口配置和连接
4. **按键无响应**: 检查按键连接和上拉电阻

### 调试方法
1. 使用UART调试输出查看系统状态
2. 观察LED指示灯状态
3. 使用示波器检查信号波形
4. 运行系统测试套件

## 扩展功能

系统设计具有良好的扩展性，可以添加:
- 更多传感器接口
- 无线通信模块
- 数据存储功能
- 远程控制功能
- 报警功能

## 版本历史

- v1.0: 基础功能实现
- v1.1: 添加状态机管理
- v1.2: 增加图形显示功能
- v1.3: 完善测试套件

## 作者信息

- 开发者: AI Assistant
- 日期: 2025-01-30
- 用途: 2025年大学生电子设计竞赛

## 许可证

本项目仅供学习和竞赛使用。
