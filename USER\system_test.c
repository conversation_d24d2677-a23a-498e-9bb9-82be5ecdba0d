#include "system_test.h"
#include "state_machine.h"
#include "ultrasonic.h"
#include "adc.h"
#include "uart_ext.h"
#include "display_manager.h"
#include "delay.h"
#include "led.h"
#include "key.h"

// 全局测试统计
TestStats_t g_test_stats;

/**
 * @brief 运行所有测试
 */
void SystemTest_RunAll(void)
{
    // 初始化测试统计
    g_test_stats.total_tests = 0;
    g_test_stats.passed_tests = 0;
    g_test_stats.failed_tests = 0;
    g_test_stats.skipped_tests = 0;
    g_test_stats.start_time = HAL_GetTick();
    
    UART_INFO("\r\n=== System Test Suite Started ===\r\n");
    
    // 基础硬件测试
    Test_PrintResult("Basic Hardware", Test_BasicHardware());
    
    // 内存系统测试
    Test_PrintResult("Memory System", Test_MemorySystem());
    
    // 时序系统测试
    Test_PrintResult("Timing System", Test_TimingSystem());
    
    // 模块功能测试
    Test_PrintResult("State Machine", Test_StateMachine());
    Test_PrintResult("Ultrasonic Sensor", Test_Ultrasonic());
    Test_PrintResult("ADC Module", Test_ADC());
    Test_PrintResult("UART Extended", Test_UartExt());
    Test_PrintResult("Display Manager", Test_DisplayManager());
    
    // 集成测试
    Test_PrintResult("System Integration", Test_Integration());
    
    g_test_stats.end_time = HAL_GetTick();
    
    // 打印测试结果
    SystemTest_PrintResults();
}

/**
 * @brief 打印测试结果
 */
void SystemTest_PrintResults(void)
{
    u32 test_duration = g_test_stats.end_time - g_test_stats.start_time;
    
    UART_INFO("\r\n=== Test Results Summary ===\r\n");
    UART_INFO("Total Tests: %d\r\n", g_test_stats.total_tests);
    UART_INFO("Passed: %d\r\n", g_test_stats.passed_tests);
    UART_INFO("Failed: %d\r\n", g_test_stats.failed_tests);
    UART_INFO("Skipped: %d\r\n", g_test_stats.skipped_tests);
    UART_INFO("Test Duration: %d ms\r\n", test_duration);
    
    if(g_test_stats.failed_tests == 0) {
        UART_INFO("*** ALL TESTS PASSED ***\r\n");
    } else {
        UART_ERROR("*** %d TESTS FAILED ***\r\n", g_test_stats.failed_tests);
    }
    UART_INFO("============================\r\n\r\n");
}

/**
 * @brief 状态机测试
 */
TestResult_t Test_StateMachine(void)
{
    Test_PrintHeader("State Machine Test");
    
    // 测试状态机初始化
    StateMachine_Init(&g_state_machine);
    Test_Assert(StateMachine_GetCurrentState(&g_state_machine) == STATE_IDLE, 
                "State machine initial state");
    
    // 测试状态转换
    StateMachine_SendEvent(&g_state_machine, EVENT_INIT);
    StateMachine_Process(&g_state_machine);
    Test_Assert(StateMachine_GetCurrentState(&g_state_machine) == STATE_INIT, 
                "State transition to INIT");
    
    // 测试超时功能
    StateMachine_SetTimeout(&g_state_machine, 100);
    delay_ms(150);
    Test_Assert(StateMachine_IsTimeout(&g_state_machine) == 1, 
                "State machine timeout");
    
    return TEST_PASS;
}

/**
 * @brief 超声波传感器测试
 */
TestResult_t Test_Ultrasonic(void)
{
    Test_PrintHeader("Ultrasonic Sensor Test");
    
    // 初始化超声波传感器
    Ultrasonic_Init();
    
    // 测试测量功能
    UltrasonicStatus_t status = Ultrasonic_Measure();
    Test_Assert(status != ULTRASONIC_ERROR, "Ultrasonic measurement");
    
    // 测试数据获取
    float distance = Ultrasonic_GetDistance_CM();
    Test_Assert(distance >= 0, "Distance value validity");
    
    UART_DEBUG("Ultrasonic distance: %.2f cm\r\n", distance);
    
    return TEST_PASS;
}

/**
 * @brief ADC模块测试
 */
TestResult_t Test_ADC(void)
{
    Test_PrintHeader("ADC Module Test");
    
    // 初始化ADC
    ADC_Init();
    ADC_Calibrate();
    
    // 测试单通道读取
    u16 adc_value;
    ADCStatus_t status = ADC_ReadChannel(0, &adc_value);
    Test_Assert(status == ADC_OK, "ADC single channel read");
    Test_Assert(adc_value <= ADC_RESOLUTION, "ADC value range");
    
    // 测试多通道读取
    status = ADC_ReadAllChannels();
    Test_Assert(status == ADC_OK, "ADC all channels read");
    
    // 测试电压转换
    float voltage = ADC_ConvertToVoltage(adc_value);
    Test_Assert(voltage >= 0 && voltage <= ADC_VREF, "Voltage conversion");
    
    UART_DEBUG("ADC CH0: %d (%.3fV)\r\n", adc_value, voltage);
    
    return TEST_PASS;
}

/**
 * @brief UART扩展功能测试
 */
TestResult_t Test_UartExt(void)
{
    Test_PrintHeader("UART Extended Test");
    
    // 初始化UART扩展
    UartExt_Init();
    
    // 测试调试级别设置
    UartExt_SetDebugLevel(DEBUG_LEVEL_DEBUG);
    Test_Assert(UartExt_GetDebugLevel() == DEBUG_LEVEL_DEBUG, "Debug level setting");
    
    // 测试格式化输出
    UartExt_Printf("Test printf: %d, %.2f\r\n", 123, 45.67f);
    
    // 测试计数器
    u32 tx_count_before = UartExt_GetTxCount();
    UartExt_Printf("Counter test\r\n");
    u32 tx_count_after = UartExt_GetTxCount();
    Test_Assert(tx_count_after > tx_count_before, "TX counter increment");
    
    return TEST_PASS;
}

/**
 * @brief 显示管理器测试
 */
TestResult_t Test_DisplayManager(void)
{
    Test_PrintHeader("Display Manager Test");
    
    // 初始化显示管理器
    DisplayManager_Init();
    
    // 测试模式切换
    DisplayManager_SetMode(DISPLAY_MODE_DEBUG);
    Test_Assert(DisplayManager_GetMode() == DISPLAY_MODE_DEBUG, "Display mode setting");
    
    // 测试更新间隔设置
    DisplayManager_SetUpdateInterval(1000);
    
    // 测试自动更新
    DisplayManager_EnableAutoUpdate(1);
    
    // 切换回主模式
    DisplayManager_SetMode(DISPLAY_MODE_MAIN);
    
    return TEST_PASS;
}

/**
 * @brief 集成测试
 */
TestResult_t Test_Integration(void)
{
    Test_PrintHeader("System Integration Test");
    
    // 测试模块间协作
    StateMachine_Init(&g_state_machine);
    StateMachine_SendEvent(&g_state_machine, EVENT_START);
    StateMachine_Process(&g_state_machine);
    
    // 执行一次完整的测量周期
    if(StateMachine_GetCurrentState(&g_state_machine) == STATE_RUNNING) {
        StateMachine_SendEvent(&g_state_machine, EVENT_TIMEOUT);
        StateMachine_Process(&g_state_machine);
        
        if(StateMachine_GetCurrentState(&g_state_machine) == STATE_MEASURING) {
            UltrasonicStatus_t status = Ultrasonic_Measure();
            if(status == ULTRASONIC_OK) {
                StateMachine_SendEvent(&g_state_machine, EVENT_SENSOR_DATA);
                StateMachine_Process(&g_state_machine);
            }
        }
    }
    
    // 读取ADC数据
    ADC_ReadAllChannels();
    
    // 更新显示
    DisplayManager_Update();
    
    Test_Assert(1, "Integration test completed");
    
    return TEST_PASS;
}

/**
 * @brief 基础硬件测试
 */
TestResult_t Test_BasicHardware(void)
{
    Test_PrintHeader("Basic Hardware Test");
    
    // 测试LED
    LED0 = 1;
    delay_ms(100);
    LED0 = 0;
    Test_Assert(1, "LED control");
    
    // 测试系统时钟
    u32 tick1 = HAL_GetTick();
    delay_ms(10);
    u32 tick2 = HAL_GetTick();
    Test_Assert((tick2 - tick1) >= 10, "System tick");
    
    return TEST_PASS;
}

/**
 * @brief 内存系统测试
 */
TestResult_t Test_MemorySystem(void)
{
    Test_PrintHeader("Memory System Test");
    
    // 测试内存分配
    void* ptr = mymalloc(SRAMIN, 1024);
    Test_Assert(ptr != NULL, "Memory allocation");
    
    if(ptr) {
        myfree(SRAMIN, ptr);
        Test_Assert(1, "Memory deallocation");
    }
    
    return TEST_PASS;
}

/**
 * @brief 时序系统测试
 */
TestResult_t Test_TimingSystem(void)
{
    Test_PrintHeader("Timing System Test");
    
    // 测试延时函数
    u32 start_time = HAL_GetTick();
    delay_ms(50);
    u32 end_time = HAL_GetTick();
    u32 elapsed = end_time - start_time;
    
    Test_Assert(elapsed >= 45 && elapsed <= 55, "Delay function accuracy");
    
    return TEST_PASS;
}

/**
 * @brief 测试断言
 */
void Test_Assert(u8 condition, const char* test_name)
{
    g_test_stats.total_tests++;
    
    if(condition) {
        g_test_stats.passed_tests++;
        UART_DEBUG("  [PASS] %s\r\n", test_name);
    } else {
        g_test_stats.failed_tests++;
        UART_ERROR("  [FAIL] %s\r\n", test_name);
    }
}

/**
 * @brief 打印测试头部
 */
void Test_PrintHeader(const char* test_name)
{
    UART_INFO("\r\n--- %s ---\r\n", test_name);
}

/**
 * @brief 打印测试结果
 */
void Test_PrintResult(const char* test_name, TestResult_t result)
{
    const char* result_str;
    
    switch(result) {
        case TEST_PASS:
            result_str = "PASS";
            break;
        case TEST_FAIL:
            result_str = "FAIL";
            break;
        case TEST_SKIP:
            result_str = "SKIP";
            break;
        case TEST_TIMEOUT:
            result_str = "TIMEOUT";
            break;
        default:
            result_str = "UNKNOWN";
            break;
    }
    
    UART_INFO("[%s] %s\r\n", result_str, test_name);
}
