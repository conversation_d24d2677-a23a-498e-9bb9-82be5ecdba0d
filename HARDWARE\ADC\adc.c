#include "adc.h"
#include "delay.h"
#include "usart.h"

// 全局ADC数据
ADCData_t g_adc_data;
ADC_HandleTypeDef hadc1;

/**
 * @brief ADC初始化
 */
void ADC_Init(void)
{
    ADC_ChannelConfTypeDef sConfig = {0};
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 使能ADC和GPIO时钟
    __HAL_RCC_ADC1_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();
    
    // 配置GPIO引脚为模拟输入
    GPIO_InitStruct.Pin = GPIO_PIN_0 | GPIO_PIN_1 | GPIO_PIN_2 | GPIO_PIN_3;
    GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
    
    // 配置ADC
    hadc1.Instance = ADC1;
    hadc1.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV4;
    hadc1.Init.Resolution = ADC_RESOLUTION_12B;
    hadc1.Init.ScanConvMode = DISABLE;
    hadc1.Init.ContinuousConvMode = DISABLE;
    hadc1.Init.DiscontinuousConvMode = DISABLE;
    hadc1.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_NONE;
    hadc1.Init.ExternalTrigConv = ADC_SOFTWARE_START;
    hadc1.Init.DataAlign = ADC_DATAALIGN_RIGHT;
    hadc1.Init.NbrOfConversion = 1;
    hadc1.Init.DMAContinuousRequests = DISABLE;
    hadc1.Init.EOCSelection = ADC_EOC_SINGLE_CONV;
    
    if (HAL_ADC_Init(&hadc1) != HAL_OK) {
        printf("ADC: Initialization failed\r\n");
        g_adc_data.status = ADC_ERROR;
        return;
    }
    
    // 初始化数据结构
    for (u8 i = 0; i < ADC_CHANNEL_COUNT; i++) {
        g_adc_data.raw_value[i] = 0;
        g_adc_data.voltage[i] = 0.0f;
        for (u8 j = 0; j < ADC_SAMPLE_COUNT; j++) {
            g_adc_data.sample_buffer[i][j] = 0;
        }
    }
    g_adc_data.sample_index = 0;
    g_adc_data.status = ADC_OK;
    g_adc_data.last_update_time = 0;
    
    printf("ADC: Initialized successfully\r\n");
}

/**
 * @brief 读取指定ADC通道
 * @param channel: ADC通道号(0-3)
 * @param value: 存储读取值的指针
 * @return ADC状态
 */
ADCStatus_t ADC_ReadChannel(u8 channel, u16* value)
{
    ADC_ChannelConfTypeDef sConfig = {0};
    u32 adc_channel;
    
    if (channel >= ADC_CHANNEL_COUNT || value == NULL) {
        return ADC_ERROR;
    }
    
    // 选择ADC通道
    switch (channel) {
        case 0: adc_channel = ADC_CHANNEL_0; break;
        case 1: adc_channel = ADC_CHANNEL_1; break;
        case 2: adc_channel = ADC_CHANNEL_2; break;
        case 3: adc_channel = ADC_CHANNEL_3; break;
        default: return ADC_ERROR;
    }
    
    // 配置ADC通道
    sConfig.Channel = adc_channel;
    sConfig.Rank = 1;
    sConfig.SamplingTime = ADC_SAMPLETIME_3CYCLES;
    
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK) {
        return ADC_ERROR;
    }
    
    // 开始转换
    if (HAL_ADC_Start(&hadc1) != HAL_OK) {
        return ADC_ERROR;
    }
    
    // 等待转换完成
    if (HAL_ADC_PollForConversion(&hadc1, 100) != HAL_OK) {
        HAL_ADC_Stop(&hadc1);
        return ADC_TIMEOUT;
    }
    
    // 读取转换结果
    *value = HAL_ADC_GetValue(&hadc1);
    
    // 停止ADC
    HAL_ADC_Stop(&hadc1);
    
    return ADC_OK;
}

/**
 * @brief 读取所有ADC通道
 * @return ADC状态
 */
ADCStatus_t ADC_ReadAllChannels(void)
{
    u16 temp_value;
    ADCStatus_t status;
    
    for (u8 i = 0; i < ADC_CHANNEL_COUNT; i++) {
        status = ADC_ReadChannel(i, &temp_value);
        if (status != ADC_OK) {
            g_adc_data.status = status;
            return status;
        }
        
        // 应用滤波
        g_adc_data.raw_value[i] = ADC_FilterValue(i, temp_value);
        g_adc_data.voltage[i] = ADC_ConvertToVoltage(g_adc_data.raw_value[i]);
    }
    
    g_adc_data.status = ADC_OK;
    g_adc_data.last_update_time = HAL_GetTick();
    
    return ADC_OK;
}

/**
 * @brief 将ADC原始值转换为电压
 * @param raw_value: ADC原始值
 * @return 电压值(V)
 */
float ADC_ConvertToVoltage(u16 raw_value)
{
    return (float)raw_value * ADC_VREF / ADC_RESOLUTION;
}

/**
 * @brief 获取指定通道的原始ADC值
 * @param channel: 通道号
 * @return 原始ADC值
 */
u16 ADC_GetRawValue(u8 channel)
{
    if (channel >= ADC_CHANNEL_COUNT) {
        return 0;
    }
    return g_adc_data.raw_value[channel];
}

/**
 * @brief 获取指定通道的电压值
 * @param channel: 通道号
 * @return 电压值(V)
 */
float ADC_GetVoltage(u8 channel)
{
    if (channel >= ADC_CHANNEL_COUNT) {
        return 0.0f;
    }
    return g_adc_data.voltage[channel];
}

/**
 * @brief 获取ADC状态
 * @return ADC状态
 */
ADCStatus_t ADC_GetStatus(void)
{
    return g_adc_data.status;
}

/**
 * @brief 对ADC值进行滤波处理
 * @param channel: 通道号
 * @param new_value: 新的ADC值
 * @return 滤波后的值
 */
u16 ADC_FilterValue(u8 channel, u16 new_value)
{
    if (channel >= ADC_CHANNEL_COUNT) {
        return new_value;
    }
    
    // 更新采样缓冲区
    g_adc_data.sample_buffer[channel][g_adc_data.sample_index] = new_value;
    
    // 计算平均值
    u32 sum = 0;
    for (u8 i = 0; i < ADC_SAMPLE_COUNT; i++) {
        sum += g_adc_data.sample_buffer[channel][i];
    }
    
    // 更新采样索引
    if (channel == ADC_CHANNEL_COUNT - 1) {
        g_adc_data.sample_index = (g_adc_data.sample_index + 1) % ADC_SAMPLE_COUNT;
    }
    
    return (u16)(sum / ADC_SAMPLE_COUNT);
}

/**
 * @brief 重置指定通道的滤波器
 * @param channel: 通道号
 */
void ADC_ResetFilter(u8 channel)
{
    if (channel >= ADC_CHANNEL_COUNT) {
        return;
    }
    
    for (u8 i = 0; i < ADC_SAMPLE_COUNT; i++) {
        g_adc_data.sample_buffer[channel][i] = 0;
    }
}

/**
 * @brief ADC校准
 */
void ADC_Calibrate(void)
{
    printf("ADC: Starting calibration...\r\n");
    
    if (HAL_ADCEx_Calibration_Start(&hadc1) != HAL_OK) {
        printf("ADC: Calibration failed\r\n");
        g_adc_data.status = ADC_ERROR;
        return;
    }
    
    printf("ADC: Calibration completed\r\n");
}

/**
 * @brief 开始ADC转换
 */
void ADC_StartConversion(void)
{
    g_adc_data.status = ADC_BUSY;
    // 这里可以添加DMA或中断方式的转换启动代码
}

/**
 * @brief 检查ADC转换是否完成
 * @return 1: 完成, 0: 未完成
 */
u8 ADC_IsConversionComplete(void)
{
    return (g_adc_data.status != ADC_BUSY);
}

/**
 * @brief 处理ADC转换
 */
void ADC_ProcessConversion(void)
{
    // 这里可以添加DMA或中断方式的转换处理代码
    if (g_adc_data.status == ADC_BUSY) {
        ADC_ReadAllChannels();
    }
}
