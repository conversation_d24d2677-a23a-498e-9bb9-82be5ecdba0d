<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [..\OBJ\Template.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image ..\OBJ\Template.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060750: Last Updated: Wed Jul 30 09:04:34 2025
<BR><P>
<H3>Maximum Stack Usage =        872 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
__rt_entry_main &rArr; main &rArr; update_font &rArr; updata_fontx &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[ac]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[3e]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3e]">ADC_IRQHandler</a><BR>
 <LI><a href="#[26]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[26]">BusFault_Handler</a><BR>
 <LI><a href="#[25]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[25]">MemManage_Handler</a><BR>
 <LI><a href="#[27]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[27]">UsageFault_Handler</a><BR>
 <LI><a href="#[19b]">usmart_strcmp</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[19b]">usmart_strcmp</a><BR>
 <LI><a href="#[1a4]">usmart_strcopy</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1a4]">usmart_strcopy</a><BR>
 <LI><a href="#[1a0]">usmart_search_nextc</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1a0]">usmart_search_nextc</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[3e]">ADC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[26]">BusFault_Handler</a> from stm32f4xx_it.o(i.BusFault_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[40]">CAN1_RX0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[41]">CAN1_RX1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[42]">CAN1_SCE_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3f]">CAN1_TX_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[6c]">CAN2_RX0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[6d]">CAN2_RX1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[6e]">CAN2_SCE_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[6b]">CAN2_TX_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[7a]">DCMI_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[37]">DMA1_Stream0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[38]">DMA1_Stream1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[39]">DMA1_Stream2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3a]">DMA1_Stream3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3b]">DMA1_Stream4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3c]">DMA1_Stream5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3d]">DMA1_Stream6_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5b]">DMA1_Stream7_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[64]">DMA2_Stream0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[65]">DMA2_Stream1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[66]">DMA2_Stream2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[67]">DMA2_Stream3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[68]">DMA2_Stream4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[70]">DMA2_Stream5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[71]">DMA2_Stream6_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[72]">DMA2_Stream7_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[29]">DebugMon_Handler</a> from stm32f4xx_it.o(i.DebugMon_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[69]">ETH_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[6a]">ETH_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[32]">EXTI0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[54]">EXTI15_10_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[33]">EXTI1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[34]">EXTI2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[35]">EXTI3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[36]">EXTI4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[43]">EXTI9_5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[30]">FLASH_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5c]">FMC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[7c]">FPU_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[7b]">HASH_RNG_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[24]">HardFault_Handler</a> from stm32f4xx_it.o(i.HardFault_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4c]">I2C1_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4b]">I2C1_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4e]">I2C2_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4d]">I2C2_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[75]">I2C3_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[74]">I2C3_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[25]">MemManage_Handler</a> from stm32f4xx_it.o(i.MemManage_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[23]">NMI_Handler</a> from stm32f4xx_it.o(i.NMI_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[6f]">OTG_FS_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[56]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[77]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[76]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[79]">OTG_HS_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[78]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2d]">PVD_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2a]">PendSV_Handler</a> from stm32f4xx_it.o(i.PendSV_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[31]">RCC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[55]">RTC_Alarm_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2f]">RTC_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[22]">Reset_Handler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5d]">SDIO_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4f]">SPI1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[50]">SPI2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5f]">SPI3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[28]">SVC_Handler</a> from stm32f4xx_it.o(i.SVC_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2b]">SysTick_Handler</a> from stm32f4xx_it.o(i.SysTick_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[7d]">SystemInit</a> from system_stm32f4xx.o(i.SystemInit) referenced from startup_stm32f407xx.o(.text)
 <LI><a href="#[2e]">TAMP_STAMP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[44]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[47]">TIM1_CC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[46]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[45]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[48]">TIM2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[49]">TIM3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4a]">TIM4_IRQHandler</a> from usmart.o(i.TIM4_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5e]">TIM5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[62]">TIM6_DAC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[63]">TIM7_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[57]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5a]">TIM8_CC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[59]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[58]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[60]">UART4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[61]">UART5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[81]">UART_DMAAbortOnError</a> from stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) referenced from stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
 <LI><a href="#[51]">USART1_IRQHandler</a> from usart.o(i.USART1_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[52]">USART2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[53]">USART3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[73]">USART6_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[27]">UsageFault_Handler</a> from stm32f4xx_it.o(i.UsageFault_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5]">W25QXX_Erase_Chip</a> from w25qxx.o(i.W25QXX_Erase_Chip) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[2c]">WWDG_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[82]">__main</a> from __main.o(!!!main) referenced from startup_stm32f407xx.o(.text)
 <LI><a href="#[80]">_printf_input_char</a> from _printf_char_common.o(.text) referenced from _printf_char_common.o(.text)
 <LI><a href="#[7f]">fputc</a> from usart.o(i.fputc) referenced from _printf_char_file.o(.text)
 <LI><a href="#[8]">mf_close</a> from fattester.o(i.mf_close) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[c]">mf_closedir</a> from fattester.o(i.mf_closedir) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[14]">mf_fmkfs</a> from fattester.o(i.mf_fmkfs) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[17]">mf_getlabel</a> from fattester.o(i.mf_getlabel) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[19]">mf_gets</a> from fattester.o(i.mf_gets) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[10]">mf_lseek</a> from fattester.o(i.mf_lseek) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[13]">mf_mkdir</a> from fattester.o(i.mf_mkdir) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[6]">mf_mount</a> from fattester.o(i.mf_mount) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[7]">mf_open</a> from fattester.o(i.mf_open) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[b]">mf_opendir</a> from fattester.o(i.mf_opendir) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[1a]">mf_putc</a> from fattester.o(i.mf_putc) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[1b]">mf_puts</a> from fattester.o(i.mf_puts) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[9]">mf_read</a> from fattester.o(i.mf_read) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[d]">mf_readdir</a> from fattester.o(i.mf_readdir) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[16]">mf_rename</a> from fattester.o(i.mf_rename) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[e]">mf_scan_files</a> from fattester.o(i.mf_scan_files) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[18]">mf_setlabel</a> from fattester.o(i.mf_setlabel) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[f]">mf_showfree</a> from fattester.o(i.mf_showfree) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[12]">mf_size</a> from fattester.o(i.mf_size) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[11]">mf_tell</a> from fattester.o(i.mf_tell) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[15]">mf_unlink</a> from fattester.o(i.mf_unlink) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[a]">mf_write</a> from fattester.o(i.mf_write) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[20]">my_mem_init</a> from malloc.o(i.my_mem_init) referenced 2 times from malloc.o(.data)
 <LI><a href="#[21]">my_mem_perused</a> from malloc.o(i.my_mem_perused) referenced 2 times from malloc.o(.data)
 <LI><a href="#[3]">read_addr</a> from usmart.o(i.read_addr) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[1d]">usmart_cmd_rec</a> from usmart.o(i.usmart_cmd_rec) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[1e]">usmart_exe</a> from usmart.o(i.usmart_exe) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[1c]">usmart_init</a> from usmart.o(i.usmart_init) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[1f]">usmart_scan</a> from usmart.o(i.usmart_scan) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[4]">write_addr</a> from usmart.o(i.write_addr) referenced 2 times from usmart_config.o(.data)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[82]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[83]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[85]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[1ac]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[1ad]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[1ae]"></a>__decompress</STRONG> (Thumb, 90 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[1af]"></a>__decompress1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[1b0]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[1b1]"></a>_printf_ll</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007))

<P><STRONG><a name="[a7]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[86]"></a>_printf_d</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_d.o(.ARM.Collect$$_printf_percent$$00000009))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_d &rArr; _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[88]"></a>_printf_u</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_u &rArr; _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[89]"></a>_printf_x</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = _printf_x &rArr; _printf_int_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
</UL>

<P><STRONG><a name="[8b]"></a>_printf_lld</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_lld &rArr; _printf_longlong_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>

<P><STRONG><a name="[8d]"></a>_printf_c</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_c.o(.ARM.Collect$$_printf_percent$$00000013))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = _printf_c &rArr; _printf_char &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
</UL>

<P><STRONG><a name="[8f]"></a>_printf_s</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_s.o(.ARM.Collect$$_printf_percent$$00000014))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = _printf_s &rArr; _printf_string &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
</UL>

<P><STRONG><a name="[1b2]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[96]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[91]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[1b3]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[1b4]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[1b5]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[1b6]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[1b7]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[1b8]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[1b9]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[1ba]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[1bb]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[1bc]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[1bd]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[1be]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[1bf]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[1c0]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[1c1]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[1c2]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[1c3]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[1c4]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[1c5]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[1c6]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[9b]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[1c7]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[1c8]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000007))

<P><STRONG><a name="[1c9]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[1ca]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000010))

<P><STRONG><a name="[1cb]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A))

<P><STRONG><a name="[1cc]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[1cd]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[84]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[1ce]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[93]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[95]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[1cf]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[97]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 872 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; update_font &rArr; updata_fontx &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[1d0]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[ad]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[9a]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[1d1]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[9c]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[1d2]"></a>WFI_SET</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, sys.o(.emb_text), UNUSED)

<P><STRONG><a name="[11e]"></a>INTX_DISABLE</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, sys.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WriteDisk
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_ReadDisk
</UL>

<P><STRONG><a name="[11f]"></a>INTX_ENABLE</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, sys.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WriteDisk
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_ReadDisk
</UL>

<P><STRONG><a name="[1d3]"></a>MSR_MSP</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sys.o(.emb_text), UNUSED)

<P><STRONG><a name="[22]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[6d]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[6e]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[7a]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[71]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[72]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[7c]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[7b]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[75]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[74]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[6f]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[77]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[76]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[79]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[78]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[73]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[ac]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32f407xx.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[1d4]"></a>__use_no_semihosting</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi_2.o(.text), UNUSED)

<P><STRONG><a name="[141]"></a>__aeabi_uldivmod</STRONG> (Thumb, 0 bytes, Stack size 48 bytes, lludivv7m.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clmt_clust
</UL>

<P><STRONG><a name="[1d5]"></a>_ll_udiv</STRONG> (Thumb, 238 bytes, Stack size 48 bytes, lludivv7m.o(.text), UNUSED)

<P><STRONG><a name="[9e]"></a>__2printf</STRONG> (Thumb, 20 bytes, Stack size 24 bytes, noretval__2printf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + Unknown Stack Size
<LI>Call Chain = __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_sys_cmd_exe
<LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_scan
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_exe
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_write
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_showfree
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_setlabel
<LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_scan_files
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_readdir
<LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_read
<LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_open
<LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_gets
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_getlabel
</UL>

<P><STRONG><a name="[a1]"></a>_printf_pre_padding</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
</UL>

<P><STRONG><a name="[a2]"></a>_printf_post_padding</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_post_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
</UL>

<P><STRONG><a name="[a0]"></a>_printf_str</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, _printf_str.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>

<P><STRONG><a name="[87]"></a>_printf_int_dec</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_u
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_d
</UL>

<P><STRONG><a name="[8c]"></a>_printf_longlong_dec</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, _printf_longlong_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _printf_longlong_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lld
</UL>

<P><STRONG><a name="[8a]"></a>_printf_int_hex</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, _printf_hex_int.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _printf_int_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_x
</UL>

<P><STRONG><a name="[1d6]"></a>_printf_longlong_hex</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, _printf_hex_int.o(.text), UNUSED)

<P><STRONG><a name="[a5]"></a>__printf</STRONG> (Thumb, 388 bytes, Stack size 40 bytes, __printf_flags_ss_wp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>

<P><STRONG><a name="[198]"></a>strcpy</STRONG> (Thumb, 72 bytes, Stack size 12 bytes, strcpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_font
</UL>

<P><STRONG><a name="[199]"></a>strcat</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, strcat.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_font
</UL>

<P><STRONG><a name="[1d7]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[1d8]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[1d9]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[1da]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[1db]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[a4]"></a>_ll_udiv10</STRONG> (Thumb, 138 bytes, Stack size 12 bytes, lludiv10.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = _ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>

<P><STRONG><a name="[a3]"></a>_printf_int_common</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[a8]"></a>_printf_cs_common</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
</UL>

<P><STRONG><a name="[8e]"></a>_printf_char</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_char &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_c
</UL>

<P><STRONG><a name="[90]"></a>_printf_string</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_string &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_s
</UL>

<P><STRONG><a name="[9f]"></a>_printf_char_file</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, _printf_char_file.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 120 + Unknown Stack Size
<LI>Call Chain = _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ferror
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[a9]"></a>_printf_char_common</STRONG> (Thumb, 32 bytes, Stack size 64 bytes, _printf_char_common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104 + Unknown Stack Size
<LI>Call Chain = _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
</UL>

<P><STRONG><a name="[aa]"></a>ferror</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ferror.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
</UL>

<P><STRONG><a name="[94]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[99]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[1dc]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[ab]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[1dd]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[26]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[df]"></a>FSMC_NORSRAM_Extended_Timing_Init</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Extended_Timing_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = FSMC_NORSRAM_Extended_Timing_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
</UL>

<P><STRONG><a name="[dd]"></a>FSMC_NORSRAM_Init</STRONG> (Thumb, 80 bytes, Stack size 20 bytes, stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = FSMC_NORSRAM_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
</UL>

<P><STRONG><a name="[de]"></a>FSMC_NORSRAM_Timing_Init</STRONG> (Thumb, 64 bytes, Stack size 12 bytes, stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Timing_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = FSMC_NORSRAM_Timing_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
</UL>

<P><STRONG><a name="[ae]"></a>Get_HzMat</STRONG> (Thumb, 136 bytes, Stack size 8 bytes, text.o(i.Get_HzMat))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = Get_HzMat &rArr; W25QXX_Read &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Font
</UL>

<P><STRONG><a name="[e6]"></a>HAL_DMA_Abort_IT</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT))
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[b0]"></a>HAL_Delay</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, stm32f4xx_hal.o(i.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[c9]"></a>HAL_GPIO_Init</STRONG> (Thumb, 402 bytes, Stack size 40 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_MspInit
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_MspInit
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Init
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SRAM_Init
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Init
</UL>

<P><STRONG><a name="[f0]"></a>HAL_GPIO_ReadPin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin))
<BR><BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Scan
</UL>

<P><STRONG><a name="[ca]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_MspInit
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
</UL>

<P><STRONG><a name="[127]"></a>HAL_GetREVID</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_GetREVID))
<BR><BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stm32_Clock_Init
</UL>

<P><STRONG><a name="[b1]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>

<P><STRONG><a name="[128]"></a>HAL_IncTick</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[b2]"></a>HAL_Init</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32f4xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b4]"></a>HAL_InitTick</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, stm32f4xx_hal.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[b5]"></a>HAL_MspInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_msp.o(i.HAL_MspInit))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[ed]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer4_Init
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>

<P><STRONG><a name="[b7]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer4_Init
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[b3]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[b9]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 356 bytes, Stack size 32 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stm32_Clock_Init
</UL>

<P><STRONG><a name="[12d]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[12c]"></a>HAL_RCC_GetPCLK2Freq</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq))
<BR><BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[ba]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 104 bytes, Stack size 12 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = HAL_RCC_GetSysClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[bb]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 766 bytes, Stack size 40 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stm32_Clock_Init
</UL>

<P><STRONG><a name="[bc]"></a>HAL_SD_ConfigWideBusOperation</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = HAL_SD_ConfigWideBusOperation &rArr; SD_WideBus_Enable &rArr; SD_FindSCR &rArr; SDMMC_CmdSendSCR &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_Init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WideBus_Enable
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WideBus_Disable
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_Init
</UL>

<P><STRONG><a name="[11c]"></a>HAL_SD_GetCardCSD</STRONG> (Thumb, 474 bytes, Stack size 20 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_GetCardCSD))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_SD_GetCardCSD
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[11a]"></a>HAL_SD_GetCardInfo</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_GetCardInfo))
<BR><BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_Init
</UL>

<P><STRONG><a name="[c0]"></a>HAL_SD_GetCardState</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_SD_GetCardState &rArr; SDMMC_CmdSendStatus &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendStatus
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_GetCardState
</UL>

<P><STRONG><a name="[c3]"></a>HAL_SD_Init</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = HAL_SD_Init &rArr; HAL_SD_InitCard &rArr; SD_InitCard &rArr; SDMMC_CmdSetRelAdd &rArr; SDMMC_GetCmdResp6 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_Init
</UL>

<P><STRONG><a name="[c5]"></a>HAL_SD_InitCard</STRONG> (Thumb, 80 bytes, Stack size 32 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_InitCard))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = HAL_SD_InitCard &rArr; SD_InitCard &rArr; SDMMC_CmdSetRelAdd &rArr; SDMMC_GetCmdResp6 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_PowerState_ON
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_Init
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
</UL>

<P><STRONG><a name="[c4]"></a>HAL_SD_MspInit</STRONG> (Thumb, 168 bytes, Stack size 48 bytes, sdio_sdcard.o(i.HAL_SD_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_SD_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
</UL>

<P><STRONG><a name="[cb]"></a>HAL_SD_ReadBlocks</STRONG> (Thumb, 436 bytes, Stack size 64 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_SD_ReadBlocks &rArr; SDMMC_CmdStopTransfer &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadSingleBlock
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadMultiBlock
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ReadFIFO
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_ReadDisk
</UL>

<P><STRONG><a name="[d2]"></a>HAL_SD_WriteBlocks</STRONG> (Thumb, 390 bytes, Stack size 64 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_SD_WriteBlocks &rArr; SDMMC_CmdWriteSingleBlock &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteSingleBlock
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteMultiBlock
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_WriteFIFO
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WriteDisk
</UL>

<P><STRONG><a name="[d6]"></a>HAL_SPI_Init</STRONG> (Thumb, 140 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>

<P><STRONG><a name="[d7]"></a>HAL_SPI_MspInit</STRONG> (Thumb, 72 bytes, Stack size 32 bytes, spi.o(i.HAL_SPI_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
</UL>

<P><STRONG><a name="[d8]"></a>HAL_SPI_TransmitReceive</STRONG> (Thumb, 608 bytes, Stack size 40 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CheckFlag_BSY
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>

<P><STRONG><a name="[db]"></a>HAL_SRAM_Init</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, stm32f4xx_hal_sram.o(i.HAL_SRAM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_SRAM_Init &rArr; HAL_SRAM_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSMC_NORSRAM_Timing_Init
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSMC_NORSRAM_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSMC_NORSRAM_Extended_Timing_Init
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SRAM_Init
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[dc]"></a>HAL_SRAM_MspInit</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, lcd.o(i.HAL_SRAM_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_SRAM_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
</UL>

<P><STRONG><a name="[150]"></a>HAL_SYSTICK_CLKSourceConfig</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig))
<BR><BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
</UL>

<P><STRONG><a name="[b6]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SYSTICK_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[e0]"></a>HAL_TIM_Base_Init</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer4_Init
</UL>

<P><STRONG><a name="[e1]"></a>HAL_TIM_Base_MspInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit))
<BR><BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[12a]"></a>HAL_TIM_Base_Start_IT</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT))
<BR><BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer4_Init
</UL>

<P><STRONG><a name="[e7]"></a>HAL_UART_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAAbortOnError
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[12e]"></a>HAL_UART_GetState</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_GetState))
<BR><BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[e3]"></a>HAL_UART_IRQHandler</STRONG> (Thumb, 266 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UART_RxCpltCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Transmit_IT
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[ea]"></a>HAL_UART_Init</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
</UL>

<P><STRONG><a name="[eb]"></a>HAL_UART_MspInit</STRONG> (Thumb, 112 bytes, Stack size 32 bytes, usart.o(i.HAL_UART_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[12f]"></a>HAL_UART_Receive_IT</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT))
<BR><BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
</UL>

<P><STRONG><a name="[12b]"></a>HAL_UART_RxCpltCallback</STRONG> (Thumb, 76 bytes, Stack size 12 bytes, usart.o(i.HAL_UART_RxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = HAL_UART_RxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
</UL>

<P><STRONG><a name="[e9]"></a>HAL_UART_TxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[24]"></a>HardFault_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.HardFault_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + Unknown Stack Size
<LI>Call Chain = HardFault_Handler &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[ee]"></a>KEY_Init</STRONG> (Thumb, 80 bytes, Stack size 40 bytes, key.o(i.KEY_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = KEY_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ef]"></a>KEY_Scan</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, key.o(i.KEY_Scan))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = KEY_Scan &rArr; delay_ms &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f2]"></a>LCD_Clear</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, lcd.o(i.LCD_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = LCD_Clear &rArr; LCD_SetCursor &rArr; LCD_WR_REG
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM_Prepare
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f5]"></a>LCD_Display_Dir</STRONG> (Thumb, 226 bytes, Stack size 36 bytes, lcd.o(i.LCD_Display_Dir))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = LCD_Display_Dir &rArr; LCD_Scan_Dir &rArr; LCD_WR_REG
</UL>
<BR>[Calls]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Scan_Dir
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[f7]"></a>LCD_Fast_DrawPoint</STRONG> (Thumb, 202 bytes, Stack size 24 bytes, lcd.o(i.LCD_Fast_DrawPoint))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = LCD_Fast_DrawPoint &rArr; LCD_WR_REG
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WR_REG
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WR_DATA
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowChar
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Font
</UL>

<P><STRONG><a name="[fa]"></a>LCD_Fill</STRONG> (Thumb, 70 bytes, Stack size 28 bytes, lcd.o(i.LCD_Fill))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = LCD_Fill &rArr; LCD_SetCursor &rArr; LCD_WR_REG
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM_Prepare
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fb]"></a>LCD_Init</STRONG> (Thumb, 11076 bytes, Stack size 120 bytes, lcd.o(i.LCD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 264 + Unknown Stack Size
<LI>Call Chain = LCD_Init &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteReg
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WR_REG
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WR_DATA
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SSD_BackLightSet
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_RD_DATA
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Display_Dir
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[105]"></a>LCD_Pow</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, lcd.o(i.LCD_Pow))
<BR><BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowNum
</UL>

<P><STRONG><a name="[fc]"></a>LCD_RD_DATA</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, lcd.o(i.LCD_RD_DATA))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LCD_RD_DATA
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[ff]"></a>LCD_SSD_BackLightSet</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, lcd.o(i.LCD_SSD_BackLightSet))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = LCD_SSD_BackLightSet &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WR_REG
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WR_DATA
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[f6]"></a>LCD_Scan_Dir</STRONG> (Thumb, 418 bytes, Stack size 16 bytes, lcd.o(i.LCD_Scan_Dir))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = LCD_Scan_Dir &rArr; LCD_WR_REG
</UL>
<BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteReg
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WR_REG
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WR_DATA
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Display_Dir
</UL>

<P><STRONG><a name="[f3]"></a>LCD_SetCursor</STRONG> (Thumb, 236 bytes, Stack size 20 bytes, lcd.o(i.LCD_SetCursor))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = LCD_SetCursor &rArr; LCD_WR_REG
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WR_REG
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WR_DATA
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Fill
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
</UL>

<P><STRONG><a name="[103]"></a>LCD_ShowChar</STRONG> (Thumb, 196 bytes, Stack size 40 bytes, lcd.o(i.LCD_ShowChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = LCD_ShowChar &rArr; LCD_Fast_DrawPoint &rArr; LCD_WR_REG
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Fast_DrawPoint
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Str
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowString
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowNum
</UL>

<P><STRONG><a name="[104]"></a>LCD_ShowNum</STRONG> (Thumb, 118 bytes, Stack size 56 bytes, lcd.o(i.LCD_ShowNum))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = LCD_ShowNum &rArr; LCD_ShowChar &rArr; LCD_Fast_DrawPoint &rArr; LCD_WR_REG
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowChar
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Pow
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fupd_prog
</UL>

<P><STRONG><a name="[106]"></a>LCD_ShowString</STRONG> (Thumb, 84 bytes, Stack size 40 bytes, lcd.o(i.LCD_ShowString))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = LCD_ShowString &rArr; LCD_ShowChar &rArr; LCD_Fast_DrawPoint &rArr; LCD_WR_REG
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowChar
</UL>
<BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_font
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fupd_prog
</UL>

<P><STRONG><a name="[f9]"></a>LCD_WR_DATA</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, lcd.o(i.LCD_WR_DATA))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LCD_WR_DATA
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Scan_Dir
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SSD_BackLightSet
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Fast_DrawPoint
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[f8]"></a>LCD_WR_REG</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, lcd.o(i.LCD_WR_REG))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LCD_WR_REG
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Scan_Dir
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SSD_BackLightSet
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Fast_DrawPoint
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[f4]"></a>LCD_WriteRAM_Prepare</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, lcd.o(i.LCD_WriteRAM_Prepare))
<BR><BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Fill
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
</UL>

<P><STRONG><a name="[fd]"></a>LCD_WriteReg</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, lcd.o(i.LCD_WriteReg))
<BR><BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Scan_Dir
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[107]"></a>LED_Init</STRONG> (Thumb, 72 bytes, Stack size 32 bytes, led.o(i.LED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = LED_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[25]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[cd]"></a>SDIO_ConfigData</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData))
<BR><BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[11b]"></a>SDIO_GetPowerState</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_GetPowerState))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[c2]"></a>SDIO_GetResponse</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SDIO_GetResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp6
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WideBus_Enable
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WideBus_Disable
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[bf]"></a>SDIO_Init</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SDIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[c6]"></a>SDIO_PowerState_ON</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_PowerState_ON))
<BR><BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[d0]"></a>SDIO_ReadFIFO</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_ReadFIFO))
<BR><BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[109]"></a>SDIO_SendCommand</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand))
<BR><BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteSingleBlock
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteMultiBlock
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSetRelAdd
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendStatus
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendSCR
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCSD
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCID
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSelDesel
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadSingleBlock
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadMultiBlock
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdOperCond
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdGoIdleState
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBusWidth
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppOperCommand
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
</UL>

<P><STRONG><a name="[d5]"></a>SDIO_WriteFIFO</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_WriteFIFO))
<BR><BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>

<P><STRONG><a name="[108]"></a>SDMMC_CmdAppCommand</STRONG> (Thumb, 48 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SDMMC_CmdAppCommand &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WideBus_Enable
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WideBus_Disable
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[10b]"></a>SDMMC_CmdAppOperCommand</STRONG> (Thumb, 46 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SDMMC_CmdAppOperCommand
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp3
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
</UL>

<P><STRONG><a name="[cc]"></a>SDMMC_CmdBlockLength</STRONG> (Thumb, 48 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SDMMC_CmdBlockLength &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[10d]"></a>SDMMC_CmdBusWidth</STRONG> (Thumb, 48 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SDMMC_CmdBusWidth &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WideBus_Enable
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WideBus_Disable
</UL>

<P><STRONG><a name="[10e]"></a>SDMMC_CmdGoIdleState</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SDMMC_CmdGoIdleState
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
</UL>

<P><STRONG><a name="[10f]"></a>SDMMC_CmdOperCond</STRONG> (Thumb, 98 bytes, Stack size 36 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdOperCond
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
</UL>

<P><STRONG><a name="[ce]"></a>SDMMC_CmdReadMultiBlock</STRONG> (Thumb, 48 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SDMMC_CmdReadMultiBlock &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[cf]"></a>SDMMC_CmdReadSingleBlock</STRONG> (Thumb, 48 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SDMMC_CmdReadSingleBlock &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[110]"></a>SDMMC_CmdSelDesel</STRONG> (Thumb, 50 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SDMMC_CmdSelDesel &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[111]"></a>SDMMC_CmdSendCID</STRONG> (Thumb, 42 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SDMMC_CmdSendCID
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp2
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[113]"></a>SDMMC_CmdSendCSD</STRONG> (Thumb, 44 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SDMMC_CmdSendCSD
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp2
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[114]"></a>SDMMC_CmdSendSCR</STRONG> (Thumb, 48 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendSCR))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SDMMC_CmdSendSCR &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[c1]"></a>SDMMC_CmdSendStatus</STRONG> (Thumb, 48 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SDMMC_CmdSendStatus &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
</UL>

<P><STRONG><a name="[115]"></a>SDMMC_CmdSetRelAdd</STRONG> (Thumb, 48 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SDMMC_CmdSetRelAdd &rArr; SDMMC_GetCmdResp6 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp6
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[d1]"></a>SDMMC_CmdStopTransfer</STRONG> (Thumb, 46 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SDMMC_CmdStopTransfer &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[d3]"></a>SDMMC_CmdWriteMultiBlock</STRONG> (Thumb, 48 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SDMMC_CmdWriteMultiBlock &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>

<P><STRONG><a name="[d4]"></a>SDMMC_CmdWriteSingleBlock</STRONG> (Thumb, 48 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SDMMC_CmdWriteSingleBlock &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>

<P><STRONG><a name="[118]"></a>SD_GetCardState</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, sdio_sdcard.o(i.SD_GetCardState))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = SD_GetCardState &rArr; HAL_SD_GetCardState &rArr; SDMMC_CmdSendStatus &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WriteDisk
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_ReadDisk
</UL>

<P><STRONG><a name="[119]"></a>SD_Init</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, sdio_sdcard.o(i.SD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = SD_Init &rArr; HAL_SD_Init &rArr; HAL_SD_InitCard &rArr; SD_InitCard &rArr; SDMMC_CmdSetRelAdd &rArr; SDMMC_GetCmdResp6 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardInfo
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[11d]"></a>SD_ReadDisk</STRONG> (Thumb, 54 bytes, Stack size 24 bytes, sdio_sdcard.o(i.SD_ReadDisk))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = SD_ReadDisk &rArr; HAL_SD_ReadBlocks &rArr; SDMMC_CmdStopTransfer &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INTX_ENABLE
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INTX_DISABLE
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_GetCardState
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>

<P><STRONG><a name="[120]"></a>SD_WriteDisk</STRONG> (Thumb, 54 bytes, Stack size 24 bytes, sdio_sdcard.o(i.SD_WriteDisk))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = SD_WriteDisk &rArr; HAL_SD_WriteBlocks &rArr; SDMMC_CmdWriteSingleBlock &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INTX_ENABLE
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INTX_DISABLE
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_GetCardState
</UL>
<BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>

<P><STRONG><a name="[121]"></a>SPI1_Init</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, spi.o(i.SPI1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = SPI1_Init &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Init
</UL>

<P><STRONG><a name="[122]"></a>SPI1_ReadWriteByte</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, spi.o(i.SPI1_ReadWriteByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write_Page
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write_Enable
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_ReadSR
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_ReadID
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Read
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Erase_Sector
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Erase_Chip
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Init
</UL>

<P><STRONG><a name="[134]"></a>SPI1_SetSpeed</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, spi.o(i.SPI1_SetSpeed))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI1_SetSpeed
</UL>
<BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Init
</UL>

<P><STRONG><a name="[123]"></a>SRAM_Init</STRONG> (Thumb, 236 bytes, Stack size 64 bytes, sram.o(i.SRAM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = SRAM_Init &rArr; HAL_SRAM_Init &rArr; HAL_SRAM_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[28]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[124]"></a>Show_Font</STRONG> (Thumb, 156 bytes, Stack size 112 bytes, text.o(i.Show_Font))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = Show_Font &rArr; Get_HzMat &rArr; W25QXX_Read &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Fast_DrawPoint
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_HzMat
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Str
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[125]"></a>Show_Str</STRONG> (Thumb, 174 bytes, Stack size 56 bytes, text.o(i.Show_Str))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = Show_Str &rArr; Show_Font &rArr; Get_HzMat &rArr; W25QXX_Read &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowChar
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Font
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[126]"></a>Stm32_Clock_Init</STRONG> (Thumb, 136 bytes, Stack size 88 bytes, sys.o(i.Stm32_Clock_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = Stm32_Clock_Init &rArr; HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetREVID
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[2b]"></a>SysTick_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[7d]"></a>SystemInit</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, system_stm32f4xx.o(i.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(.text)
</UL>
<P><STRONG><a name="[4a]"></a>TIM4_IRQHandler</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, usmart.o(i.TIM4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM4_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[e2]"></a>TIM_Base_SetConfig</STRONG> (Thumb, 148 bytes, Stack size 20 bytes, stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[129]"></a>Timer4_Init</STRONG> (Thumb, 72 bytes, Stack size 24 bytes, usmart.o(i.Timer4_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = Timer4_Init &rArr; HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start_IT
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_init
</UL>

<P><STRONG><a name="[51]"></a>USART1_IRQHandler</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, usart.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = USART1_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UART_RxCpltCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_IT
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_GetState
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>W25QXX_Erase_Chip</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, w25qxx.o(i.W25QXX_Erase_Chip))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = W25QXX_Erase_Chip &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write_Enable
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Wait_Busy
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[132]"></a>W25QXX_Erase_Sector</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, w25qxx.o(i.W25QXX_Erase_Sector))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = W25QXX_Erase_Sector &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write_Enable
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Wait_Busy
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_font
</UL>

<P><STRONG><a name="[133]"></a>W25QXX_Init</STRONG> (Thumb, 108 bytes, Stack size 40 bytes, w25qxx.o(i.W25QXX_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = W25QXX_Init &rArr; W25QXX_ReadID &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_ReadSR
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_ReadID
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_SetSpeed
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;font_init
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[af]"></a>W25QXX_Read</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, w25qxx.o(i.W25QXX_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = W25QXX_Read &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_font
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;font_init
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_HzMat
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>

<P><STRONG><a name="[135]"></a>W25QXX_ReadID</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, w25qxx.o(i.W25QXX_ReadID))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = W25QXX_ReadID &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Init
</UL>

<P><STRONG><a name="[136]"></a>W25QXX_ReadSR</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, w25qxx.o(i.W25QXX_ReadSR))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Wait_Busy
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Init
</UL>

<P><STRONG><a name="[131]"></a>W25QXX_Wait_Busy</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, w25qxx.o(i.W25QXX_Wait_Busy))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_ReadSR
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write_Page
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Erase_Sector
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Erase_Chip
</UL>

<P><STRONG><a name="[137]"></a>W25QXX_Write</STRONG> (Thumb, 154 bytes, Stack size 40 bytes, w25qxx.o(i.W25QXX_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 200 + Unknown Stack Size
<LI>Call Chain = W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write_NoCheck
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Read
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Erase_Sector
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_font
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;updata_fontx
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>

<P><STRONG><a name="[130]"></a>W25QXX_Write_Enable</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, w25qxx.o(i.W25QXX_Write_Enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = W25QXX_Write_Enable &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write_Page
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Erase_Sector
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Erase_Chip
</UL>

<P><STRONG><a name="[138]"></a>W25QXX_Write_NoCheck</STRONG> (Thumb, 62 bytes, Stack size 24 bytes, w25qxx.o(i.W25QXX_Write_NoCheck))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write_Page
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write
</UL>

<P><STRONG><a name="[139]"></a>W25QXX_Write_Page</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, w25qxx.o(i.W25QXX_Write_Page))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write_Enable
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Wait_Busy
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write_NoCheck
</UL>

<P><STRONG><a name="[a6]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, __printf_wp.o(i._is_digit))
<BR><BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[9d]"></a>_sys_exit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, usart.o(i._sys_exit))
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[14f]"></a>delay_init</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, delay.o(i.delay_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = delay_init
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_CLKSourceConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f1]"></a>delay_ms</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, delay.o(i.delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = delay_ms &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;font_init
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Scan
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fe]"></a>delay_us</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, delay.o(i.delay_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
</UL>

<P><STRONG><a name="[163]"></a>disk_initialize</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, diskio.o(i.disk_initialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = disk_initialize &rArr; SD_Init &rArr; HAL_SD_Init &rArr; HAL_SD_InitCard &rArr; SD_InitCard &rArr; SDMMC_CmdSetRelAdd &rArr; SDMMC_GetCmdResp6 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Init
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[17d]"></a>disk_ioctl</STRONG> (Thumb, 86 bytes, Stack size 0 bytes, diskio.o(i.disk_ioctl))
<BR><BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
</UL>

<P><STRONG><a name="[164]"></a>disk_read</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, diskio.o(i.disk_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = disk_read &rArr; SD_Init &rArr; HAL_SD_Init &rArr; HAL_SD_InitCard &rArr; SD_InitCard &rArr; SDMMC_CmdSetRelAdd &rArr; SDMMC_GetCmdResp6 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_ReadDisk
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Read
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>

<P><STRONG><a name="[18d]"></a>disk_status</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, diskio.o(i.disk_status))
<BR><BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[165]"></a>disk_write</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, diskio.o(i.disk_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 224 + Unknown Stack Size
<LI>Call Chain = disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WriteDisk
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
</UL>

<P><STRONG><a name="[166]"></a>exfuns_init</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, exfuns.o(i.exfuns_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = exfuns_init &rArr; mymalloc &rArr; my_mem_malloc
</UL>
<BR>[Calls]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mymalloc
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[168]"></a>f_close</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, ff.o(i.f_close))
<BR><BR>[Stack]<UL><LI>Max Depth = 504 + Unknown Stack Size
<LI>Call Chain = f_close &rArr; f_sync &rArr; load_obj_dir &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
</UL>
<BR>[Called By]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;updata_fontx
<LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_close
</UL>

<P><STRONG><a name="[16b]"></a>f_closedir</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, ff.o(i.f_closedir))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = f_closedir &rArr; validate
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
</UL>
<BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_closedir
</UL>

<P><STRONG><a name="[16c]"></a>f_getfree</STRONG> (Thumb, 280 bytes, Stack size 96 bytes, ff.o(i.f_getfree))
<BR><BR>[Stack]<UL><LI>Max Depth = 432 + Unknown Stack Size
<LI>Call Chain = f_getfree &rArr; find_volume &rArr; check_fs &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>
<BR>[Called By]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_showfree
</UL>

<P><STRONG><a name="[16e]"></a>f_getlabel</STRONG> (Thumb, 242 bytes, Stack size 120 bytes, ff.o(i.f_getlabel))
<BR><BR>[Stack]<UL><LI>Max Depth = 536 + Unknown Stack Size
<LI>Call Chain = f_getlabel &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
</UL>
<BR>[Called By]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_getlabel
</UL>

<P><STRONG><a name="[16f]"></a>f_gets</STRONG> (Thumb, 68 bytes, Stack size 32 bytes, ff.o(i.f_gets))
<BR><BR>[Stack]<UL><LI>Max Depth = 368 + Unknown Stack Size
<LI>Call Chain = f_gets &rArr; f_read &rArr; get_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
</UL>
<BR>[Called By]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_gets
</UL>

<P><STRONG><a name="[171]"></a>f_lseek</STRONG> (Thumb, 700 bytes, Stack size 64 bytes, ff.o(i.f_lseek))
<BR><BR>[Stack]<UL><LI>Max Depth = 392 + Unknown Stack Size
<LI>Call Chain = f_lseek &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clmt_clust
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_lseek
</UL>

<P><STRONG><a name="[172]"></a>f_mkdir</STRONG> (Thumb, 476 bytes, Stack size 144 bytes, ff.o(i.f_mkdir))
<BR><BR>[Stack]<UL><LI>Max Depth = 720 + Unknown Stack Size
<LI>Call Chain = f_mkdir &rArr; dir_register &rArr; dir_find &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;store_xdir
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_dword
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_clust
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_mkdir
</UL>

<P><STRONG><a name="[17b]"></a>f_mkfs</STRONG> (Thumb, 1216 bytes, Stack size 80 bytes, ff.o(i.f_mkfs))
<BR><BR>[Stack]<UL><LI>Max Depth = 304 + Unknown Stack Size
<LI>Call Chain = f_mkfs &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_ioctl
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_word
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_dword
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ldnumber
</UL>
<BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_fmkfs
</UL>

<P><STRONG><a name="[17e]"></a>f_mount</STRONG> (Thumb, 72 bytes, Stack size 24 bytes, ff.o(i.f_mount))
<BR><BR>[Stack]<UL><LI>Max Depth = 360 + Unknown Stack Size
<LI>Call Chain = f_mount &rArr; find_volume &rArr; check_fs &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ldnumber
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_mount
</UL>

<P><STRONG><a name="[17f]"></a>f_open</STRONG> (Thumb, 630 bytes, Stack size 144 bytes, ff.o(i.f_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 720 + Unknown Stack Size
<LI>Call Chain = f_open &rArr; dir_register &rArr; dir_find &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;store_xdir
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_qword
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_dword
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_clust
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_qword
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_clust
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>
<BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_font
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;updata_fontx
<LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_open
</UL>

<P><STRONG><a name="[182]"></a>f_opendir</STRONG> (Thumb, 212 bytes, Stack size 40 bytes, ff.o(i.f_opendir))
<BR><BR>[Stack]<UL><LI>Max Depth = 520 + Unknown Stack Size
<LI>Call Chain = f_opendir &rArr; follow_path &rArr; dir_find &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_qword
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_clust
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
</UL>
<BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_scan_files
<LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_opendir
</UL>

<P><STRONG><a name="[183]"></a>f_putc</STRONG> (Thumb, 60 bytes, Stack size 88 bytes, ff.o(i.f_putc))
<BR><BR>[Stack]<UL><LI>Max Depth = 480 + Unknown Stack Size
<LI>Call Chain = f_putc &rArr; putc_bfd &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;putc_bfd
</UL>
<BR>[Called By]<UL><LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_putc
</UL>

<P><STRONG><a name="[186]"></a>f_puts</STRONG> (Thumb, 70 bytes, Stack size 96 bytes, ff.o(i.f_puts))
<BR><BR>[Stack]<UL><LI>Max Depth = 488 + Unknown Stack Size
<LI>Call Chain = f_puts &rArr; putc_bfd &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;putc_bfd
</UL>
<BR>[Called By]<UL><LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_puts
</UL>

<P><STRONG><a name="[170]"></a>f_read</STRONG> (Thumb, 374 bytes, Stack size 48 bytes, ff.o(i.f_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 336 + Unknown Stack Size
<LI>Call Chain = f_read &rArr; get_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clmt_clust
</UL>
<BR>[Called By]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;updata_fontx
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_gets
<LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_read
</UL>

<P><STRONG><a name="[187]"></a>f_readdir</STRONG> (Thumb, 118 bytes, Stack size 40 bytes, ff.o(i.f_readdir))
<BR><BR>[Stack]<UL><LI>Max Depth = 456 + Unknown Stack Size
<LI>Call Chain = f_readdir &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fileinfo
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>
<BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_scan_files
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_readdir
</UL>

<P><STRONG><a name="[189]"></a>f_rename</STRONG> (Thumb, 398 bytes, Stack size 256 bytes, ff.o(i.f_rename))
<BR><BR>[Stack]<UL><LI>Max Depth = 832 + Unknown Stack Size
<LI>Call Chain = f_rename &rArr; dir_register &rArr; dir_find &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;store_xdir
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_word
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_clust
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_clust
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ldnumber
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_remove
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
</UL>
<BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_rename
</UL>

<P><STRONG><a name="[18a]"></a>f_setlabel</STRONG> (Thumb, 460 bytes, Stack size 136 bytes, ff.o(i.f_setlabel))
<BR><BR>[Stack]<UL><LI>Max Depth = 552 + Unknown Stack Size
<LI>Call Chain = f_setlabel &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_word
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_alloc
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_chr
</UL>
<BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_setlabel
</UL>

<P><STRONG><a name="[169]"></a>f_sync</STRONG> (Thumb, 338 bytes, Stack size 96 bytes, ff.o(i.f_sync))
<BR><BR>[Stack]<UL><LI>Max Depth = 488 + Unknown Stack Size
<LI>Call Chain = f_sync &rArr; load_obj_dir &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;store_xdir
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_word
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_qword
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_dword
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_clust
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_obj_dir
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fill_fat_chain
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
</UL>

<P><STRONG><a name="[18b]"></a>f_unlink</STRONG> (Thumb, 266 bytes, Stack size 224 bytes, ff.o(i.f_unlink))
<BR><BR>[Stack]<UL><LI>Max Depth = 704 + Unknown Stack Size
<LI>Call Chain = f_unlink &rArr; follow_path &rArr; dir_find &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_qword
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_clust
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_remove
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
</UL>
<BR>[Called By]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_unlink
</UL>

<P><STRONG><a name="[185]"></a>f_write</STRONG> (Thumb, 468 bytes, Stack size 48 bytes, ff.o(i.f_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 376 + Unknown Stack Size
<LI>Call Chain = f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clmt_clust
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_puts
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_putc
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;putc_bfd
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mf_write
</UL>

<P><STRONG><a name="[149]"></a>ff_convert</STRONG> (Thumb, 106 bytes, Stack size 40 bytes, mycc936.o(i.ff_convert))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = ff_convert &rArr; W25QXX_Read &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getlabel
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_xdir_info
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fileinfo
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[173]"></a>ff_memalloc</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, diskio.o(i.ff_memalloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ff_memalloc &rArr; mymalloc &rArr; my_mem_malloc
</UL>
<BR>[Calls]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mymalloc
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[179]"></a>ff_memfree</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, diskio.o(i.ff_memfree))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ff_memfree &rArr; myfree &rArr; my_mem_free
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myfree
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[143]"></a>ff_wtoupper</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, mycc936.o(i.ff_wtoupper))
<BR><BR>[Called By]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xname_sum
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmp_lfn
</UL>

<P><STRONG><a name="[18e]"></a>font_init</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, fontupd.o(i.font_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = font_init &rArr; W25QXX_Init &rArr; W25QXX_ReadID &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Read
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7f]"></a>fputc</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, usart.o(i.fputc))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_file.o(.text)
</UL>
<P><STRONG><a name="[18f]"></a>fupd_prog</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, fontupd.o(i.fupd_prog))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = fupd_prog &rArr; LCD_ShowNum &rArr; LCD_ShowChar &rArr; LCD_Fast_DrawPoint &rArr; LCD_WR_REG
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowString
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowNum
</UL>
<BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_font
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;updata_fontx
</UL>

<P><STRONG><a name="[175]"></a>get_fattime</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, diskio.o(i.get_fattime))
<BR><BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[98]"></a>main</STRONG> (Thumb, 784 bytes, Stack size 24 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 872 + Unknown Stack Size
<LI>Call Chain = main &rArr; update_font &rArr; updata_fontx &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_font
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_mem_init
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;font_init
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exfuns_init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Init
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stm32_Clock_Init
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Str
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Font
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SRAM_Init
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_Init
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowString
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowNum
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Init
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Fill
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Scan
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[8]"></a>mf_close</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, fattester.o(i.mf_close))
<BR><BR>[Stack]<UL><LI>Max Depth = 512 + Unknown Stack Size
<LI>Call Chain = mf_close &rArr; f_close &rArr; f_sync &rArr; load_obj_dir &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[c]"></a>mf_closedir</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, fattester.o(i.mf_closedir))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = mf_closedir &rArr; f_closedir &rArr; validate
</UL>
<BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_closedir
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[14]"></a>mf_fmkfs</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, fattester.o(i.mf_fmkfs))
<BR><BR>[Stack]<UL><LI>Max Depth = 304 + Unknown Stack Size
<LI>Call Chain = mf_fmkfs &rArr; f_mkfs &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[17]"></a>mf_getlabel</STRONG> (Thumb, 56 bytes, Stack size 32 bytes, fattester.o(i.mf_getlabel))
<BR><BR>[Stack]<UL><LI>Max Depth = 568 + Unknown Stack Size
<LI>Call Chain = mf_getlabel &rArr; f_getlabel &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getlabel
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[19]"></a>mf_gets</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, fattester.o(i.mf_gets))
<BR><BR>[Stack]<UL><LI>Max Depth = 376 + Unknown Stack Size
<LI>Call Chain = mf_gets &rArr; f_gets &rArr; f_read &rArr; get_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_gets
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[10]"></a>mf_lseek</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, fattester.o(i.mf_lseek))
<BR><BR>[Stack]<UL><LI>Max Depth = 392 + Unknown Stack Size
<LI>Call Chain = mf_lseek &rArr; f_lseek &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[13]"></a>mf_mkdir</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, fattester.o(i.mf_mkdir))
<BR><BR>[Stack]<UL><LI>Max Depth = 720 + Unknown Stack Size
<LI>Call Chain = mf_mkdir &rArr; f_mkdir &rArr; dir_register &rArr; dir_find &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[6]"></a>mf_mount</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, fattester.o(i.mf_mount))
<BR><BR>[Stack]<UL><LI>Max Depth = 360 + Unknown Stack Size
<LI>Call Chain = mf_mount &rArr; f_mount &rArr; find_volume &rArr; check_fs &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[7]"></a>mf_open</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, fattester.o(i.mf_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 736 + Unknown Stack Size
<LI>Call Chain = mf_open &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[b]"></a>mf_opendir</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, fattester.o(i.mf_opendir))
<BR><BR>[Stack]<UL><LI>Max Depth = 520 + Unknown Stack Size
<LI>Call Chain = mf_opendir &rArr; f_opendir &rArr; follow_path &rArr; dir_find &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[1a]"></a>mf_putc</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, fattester.o(i.mf_putc))
<BR><BR>[Stack]<UL><LI>Max Depth = 488 + Unknown Stack Size
<LI>Call Chain = mf_putc &rArr; f_putc &rArr; putc_bfd &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_putc
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[1b]"></a>mf_puts</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, fattester.o(i.mf_puts))
<BR><BR>[Stack]<UL><LI>Max Depth = 496 + Unknown Stack Size
<LI>Call Chain = mf_puts &rArr; f_puts &rArr; putc_bfd &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_puts
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[9]"></a>mf_read</STRONG> (Thumb, 210 bytes, Stack size 40 bytes, fattester.o(i.mf_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 376 + Unknown Stack Size
<LI>Call Chain = mf_read &rArr; f_read &rArr; get_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[d]"></a>mf_readdir</STRONG> (Thumb, 150 bytes, Stack size 8 bytes, fattester.o(i.mf_readdir))
<BR><BR>[Stack]<UL><LI>Max Depth = 464 + Unknown Stack Size
<LI>Call Chain = mf_readdir &rArr; f_readdir &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[16]"></a>mf_rename</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, fattester.o(i.mf_rename))
<BR><BR>[Stack]<UL><LI>Max Depth = 832 + Unknown Stack Size
<LI>Call Chain = mf_rename &rArr; f_rename &rArr; dir_register &rArr; dir_find &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[e]"></a>mf_scan_files</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, fattester.o(i.mf_scan_files))
<BR><BR>[Stack]<UL><LI>Max Depth = 536 + Unknown Stack Size
<LI>Call Chain = mf_scan_files &rArr; f_opendir &rArr; follow_path &rArr; dir_find &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[18]"></a>mf_setlabel</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, fattester.o(i.mf_setlabel))
<BR><BR>[Stack]<UL><LI>Max Depth = 560 + Unknown Stack Size
<LI>Call Chain = mf_setlabel &rArr; f_setlabel &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[f]"></a>mf_showfree</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, fattester.o(i.mf_showfree))
<BR><BR>[Stack]<UL><LI>Max Depth = 448 + Unknown Stack Size
<LI>Call Chain = mf_showfree &rArr; f_getfree &rArr; find_volume &rArr; check_fs &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[12]"></a>mf_size</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, fattester.o(i.mf_size))
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[11]"></a>mf_tell</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, fattester.o(i.mf_tell))
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[15]"></a>mf_unlink</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, fattester.o(i.mf_unlink))
<BR><BR>[Stack]<UL><LI>Max Depth = 704 + Unknown Stack Size
<LI>Call Chain = mf_unlink &rArr; f_unlink &rArr; follow_path &rArr; dir_find &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[a]"></a>mf_write</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, fattester.o(i.mf_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 392 + Unknown Stack Size
<LI>Call Chain = mf_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[195]"></a>my_mem_free</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, malloc.o(i.my_mem_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = my_mem_free
</UL>
<BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myfree
</UL>

<P><STRONG><a name="[20]"></a>my_mem_init</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, malloc.o(i.my_mem_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = my_mem_init
</UL>
<BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mymemset
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>
<BR>[Address Reference Count : 1]<UL><LI> malloc.o(.data)
</UL>
<P><STRONG><a name="[196]"></a>my_mem_malloc</STRONG> (Thumb, 132 bytes, Stack size 24 bytes, malloc.o(i.my_mem_malloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = my_mem_malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mymalloc
</UL>

<P><STRONG><a name="[21]"></a>my_mem_perused</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, malloc.o(i.my_mem_perused))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = my_mem_perused
</UL>
<BR>[Address Reference Count : 1]<UL><LI> malloc.o(.data)
</UL>
<P><STRONG><a name="[18c]"></a>myfree</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, malloc.o(i.myfree))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = myfree &rArr; my_mem_free
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_mem_free
</UL>
<BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_font
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;updata_fontx
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
</UL>

<P><STRONG><a name="[167]"></a>mymalloc</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, malloc.o(i.mymalloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = mymalloc &rArr; my_mem_malloc
</UL>
<BR>[Calls]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_mem_malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_font
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exfuns_init
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;updata_fontx
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
</UL>

<P><STRONG><a name="[194]"></a>mymemset</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, malloc.o(i.mymemset))
<BR><BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_mem_init
</UL>

<P><STRONG><a name="[3]"></a>read_addr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, usmart.o(i.read_addr))
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[192]"></a>uart_init</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, usart.o(i.uart_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = uart_init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_IT
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[197]"></a>updata_fontx</STRONG> (Thumb, 232 bytes, Stack size 64 bytes, fontupd.o(i.updata_fontx))
<BR><BR>[Stack]<UL><LI>Max Depth = 784 + Unknown Stack Size
<LI>Call Chain = updata_fontx &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fupd_prog
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mymalloc
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myfree
</UL>
<BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_font
</UL>

<P><STRONG><a name="[193]"></a>update_font</STRONG> (Thumb, 664 bytes, Stack size 64 bytes, fontupd.o(i.update_font))
<BR><BR>[Stack]<UL><LI>Max Depth = 848 + Unknown Stack Size
<LI>Call Chain = update_font &rArr; updata_fontx &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Write
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Read
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Erase_Sector
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowString
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcat
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;updata_fontx
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fupd_prog
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mymalloc
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myfree
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1d]"></a>usmart_cmd_rec</STRONG> (Thumb, 144 bytes, Stack size 96 bytes, usmart.o(i.usmart_cmd_rec))
<BR><BR>[Stack]<UL><LI>Max Depth = 364<LI>Call Chain = usmart_cmd_rec &rArr; usmart_get_fparam &rArr; usmart_str2num
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_strcmp
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_fparam
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_fname
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[1e]"></a>usmart_exe</STRONG> (Thumb, 510 bytes, Stack size 128 bytes, usmart.o(i.usmart_exe))
<BR><BR>[Stack]<UL><LI>Max Depth = 272 + Unknown Stack Size
<LI>Call Chain = usmart_exe &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_parmpos
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_fname
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_reset_runtime
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_runtime
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[1a1]"></a>usmart_get_aparm</STRONG> (Thumb, 164 bytes, Stack size 20 bytes, usmart_str.o(i.usmart_get_aparm))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = usmart_get_aparm
</UL>
<BR>[Called By]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_fparam
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_sys_cmd_exe
</UL>

<P><STRONG><a name="[1a7]"></a>usmart_get_cmdname</STRONG> (Thumb, 46 bytes, Stack size 12 bytes, usmart_str.o(i.usmart_get_cmdname))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = usmart_get_cmdname
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_sys_cmd_exe
</UL>

<P><STRONG><a name="[19a]"></a>usmart_get_fname</STRONG> (Thumb, 352 bytes, Stack size 60 bytes, usmart_str.o(i.usmart_get_fname))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = usmart_get_fname
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_search_nextc
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_sys_cmd_exe
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_exe
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_cmd_rec
</UL>

<P><STRONG><a name="[19c]"></a>usmart_get_fparam</STRONG> (Thumb, 202 bytes, Stack size 240 bytes, usmart_str.o(i.usmart_get_fparam))
<BR><BR>[Stack]<UL><LI>Max Depth = 268<LI>Call Chain = usmart_get_fparam &rArr; usmart_str2num
</UL>
<BR>[Calls]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_strlen
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_strcopy
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_str2num
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_parmpos
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_aparm
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_cmd_rec
</UL>

<P><STRONG><a name="[19d]"></a>usmart_get_parmpos</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, usmart_str.o(i.usmart_get_parmpos))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usmart_get_parmpos
</UL>
<BR>[Called By]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_fparam
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_exe
</UL>

<P><STRONG><a name="[19f]"></a>usmart_get_runtime</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, usmart.o(i.usmart_get_runtime))
<BR><BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_exe
</UL>

<P><STRONG><a name="[1c]"></a>usmart_init</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, usmart.o(i.usmart_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = usmart_init &rArr; Timer4_Init &rArr; HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer4_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[1a6]"></a>usmart_pow</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, usmart_str.o(i.usmart_pow))
<BR><BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_str2num
</UL>

<P><STRONG><a name="[19e]"></a>usmart_reset_runtime</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, usmart.o(i.usmart_reset_runtime))
<BR><BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_exe
</UL>

<P><STRONG><a name="[1f]"></a>usmart_scan</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, usmart.o(i.usmart_scan))
<BR><BR>[Stack]<UL><LI>Max Depth = 240 + Unknown Stack Size
<LI>Call Chain = usmart_scan &rArr; usmart_sys_cmd_exe &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_sys_cmd_exe
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[1a0]"></a>usmart_search_nextc</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, usmart_str.o(i.usmart_search_nextc))
<BR><BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_search_nextc
</UL>
<BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_search_nextc
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_fname
</UL>

<P><STRONG><a name="[1a2]"></a>usmart_str2num</STRONG> (Thumb, 214 bytes, Stack size 28 bytes, usmart_str.o(i.usmart_str2num))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = usmart_str2num
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_pow
</UL>
<BR>[Called By]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_fparam
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_sys_cmd_exe
</UL>

<P><STRONG><a name="[19b]"></a>usmart_strcmp</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, usmart_str.o(i.usmart_strcmp))
<BR><BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_strcmp
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_fname
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_sys_cmd_exe
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_cmd_rec
</UL>

<P><STRONG><a name="[1a4]"></a>usmart_strcopy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, usmart_str.o(i.usmart_strcopy))
<BR><BR>[Calls]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_strcopy
</UL>
<BR>[Called By]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_strcopy
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_fparam
</UL>

<P><STRONG><a name="[1a3]"></a>usmart_strlen</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, usmart_str.o(i.usmart_strlen))
<BR><BR>[Called By]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_fparam
</UL>

<P><STRONG><a name="[1a5]"></a>usmart_sys_cmd_exe</STRONG> (Thumb, 1458 bytes, Stack size 72 bytes, usmart.o(i.usmart_sys_cmd_exe))
<BR><BR>[Stack]<UL><LI>Max Depth = 216 + Unknown Stack Size
<LI>Call Chain = usmart_sys_cmd_exe &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_strcmp
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_str2num
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_fname
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_cmdname
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_aparm
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_scan
</UL>

<P><STRONG><a name="[4]"></a>write_addr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, usmart.o(i.write_addr))
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[102]"></a>__aeabi_d2uiz</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dfixu.o(x$fpl$dfixu))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_d2uiz
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SSD_BackLightSet
</UL>

<P><STRONG><a name="[1a8]"></a>_dfixu</STRONG> (Thumb, 90 bytes, Stack size 32 bytes, dfixu.o(x$fpl$dfixu), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[100]"></a>__aeabi_ui2d</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dfltu))
<BR><BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SSD_BackLightSet
</UL>

<P><STRONG><a name="[1de]"></a>_dfltu</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dfltu), UNUSED)

<P><STRONG><a name="[101]"></a>__aeabi_dmul</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SSD_BackLightSet
</UL>

<P><STRONG><a name="[1aa]"></a>_dmul</STRONG> (Thumb, 332 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[1a9]"></a>__fpl_dnaninf</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, dnaninf.o(x$fpl$dnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dfixu
</UL>

<P><STRONG><a name="[1ab]"></a>__fpl_dretinf</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dretinf.o(x$fpl$dretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
</UL>

<P><STRONG><a name="[92]"></a>_fp_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[1df]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[1e0]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[b8]"></a>NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.NVIC_SetPriority))
<BR><BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>

<P><STRONG><a name="[81]"></a>UART_DMAAbortOnError</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_DMAAbortOnError
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
</UL>
<P><STRONG><a name="[e5]"></a>UART_EndRxTransfer</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.UART_EndRxTransfer))
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[e4]"></a>UART_Receive_IT</STRONG> (Thumb, 140 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_Receive_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = UART_Receive_IT &rArr; HAL_UART_RxCpltCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[ec]"></a>UART_SetConfig</STRONG> (Thumb, 676 bytes, Stack size 24 bytes, stm32f4xx_hal_uart.o(i.UART_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = UART_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[e8]"></a>UART_Transmit_IT</STRONG> (Thumb, 98 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.UART_Transmit_IT))
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[da]"></a>SPI_CheckFlag_BSY</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>

<P><STRONG><a name="[d9]"></a>SPI_WaitFlagStateUntilTimeout</STRONG> (Thumb, 150 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CheckFlag_BSY
</UL>

<P><STRONG><a name="[117]"></a>SD_FindSCR</STRONG> (Thumb, 214 bytes, Stack size 72 bytes, stm32f4xx_hal_sd.o(i.SD_FindSCR))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = SD_FindSCR &rArr; SDMMC_CmdSendSCR &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendSCR
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ReadFIFO
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WideBus_Enable
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_WideBus_Disable
</UL>

<P><STRONG><a name="[c8]"></a>SD_InitCard</STRONG> (Thumb, 230 bytes, Stack size 72 bytes, stm32f4xx_hal_sd.o(i.SD_InitCard))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = SD_InitCard &rArr; SDMMC_CmdSetRelAdd &rArr; SDMMC_GetCmdResp6 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSetRelAdd
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCSD
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCID
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSelDesel
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_Init
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetPowerState
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardCSD
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[c7]"></a>SD_PowerON</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, stm32f4xx_hal_sd.o(i.SD_PowerON))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = SD_PowerON &rArr; SDMMC_CmdAppCommand &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdOperCond
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdGoIdleState
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppOperCommand
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[be]"></a>SD_WideBus_Disable</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_hal_sd.o(i.SD_WideBus_Disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = SD_WideBus_Disable &rArr; SD_FindSCR &rArr; SDMMC_CmdSendSCR &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBusWidth
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
</UL>

<P><STRONG><a name="[bd]"></a>SD_WideBus_Enable</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_hal_sd.o(i.SD_WideBus_Enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = SD_WideBus_Enable &rArr; SD_FindSCR &rArr; SDMMC_CmdSendSCR &rArr; SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBusWidth
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
</UL>

<P><STRONG><a name="[10a]"></a>SDMMC_GetCmdResp1</STRONG> (Thumb, 276 bytes, Stack size 8 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SDMMC_GetCmdResp1 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteSingleBlock
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteMultiBlock
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendStatus
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendSCR
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSelDesel
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadSingleBlock
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadMultiBlock
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBusWidth
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
</UL>

<P><STRONG><a name="[112]"></a>SDMMC_GetCmdResp2</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2))
<BR><BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCSD
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCID
</UL>

<P><STRONG><a name="[10c]"></a>SDMMC_GetCmdResp3</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppOperCommand
</UL>

<P><STRONG><a name="[116]"></a>SDMMC_GetCmdResp6</STRONG> (Thumb, 128 bytes, Stack size 8 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SDMMC_GetCmdResp6 &rArr; SDIO_GetResponse
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSetRelAdd
</UL>

<P><STRONG><a name="[13a]"></a>change_bitmap</STRONG> (Thumb, 118 bytes, Stack size 32 bytes, ff.o(i.change_bitmap))
<BR><BR>[Stack]<UL><LI>Max Depth = 296 + Unknown Stack Size
<LI>Call Chain = change_bitmap &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>

<P><STRONG><a name="[13c]"></a>check_fs</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, ff.o(i.check_fs))
<BR><BR>[Stack]<UL><LI>Max Depth = 280 + Unknown Stack Size
<LI>Call Chain = check_fs &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cmp
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[14a]"></a>chk_chr</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, ff.o(i.chk_chr))
<BR><BR>[Called By]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[140]"></a>clmt_clust</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, ff.o(i.clmt_clust))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = clmt_clust &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
</UL>

<P><STRONG><a name="[158]"></a>clust2sect</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, ff.o(i.clust2sect))
<BR><BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>

<P><STRONG><a name="[142]"></a>cmp_lfn</STRONG> (Thumb, 128 bytes, Stack size 40 bytes, ff.o(i.cmp_lfn))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = cmp_lfn
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
</UL>
<BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[144]"></a>create_chain</STRONG> (Thumb, 270 bytes, Stack size 32 bytes, ff.o(i.create_chain))
<BR><BR>[Stack]<UL><LI>Max Depth = 328 + Unknown Stack Size
<LI>Call Chain = create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_bitmap
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;change_bitmap
</UL>
<BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>

<P><STRONG><a name="[148]"></a>create_name</STRONG> (Thumb, 514 bytes, Stack size 48 bytes, ff.o(i.create_name))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = create_name &rArr; ff_convert &rArr; W25QXX_Read &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_chr
</UL>
<BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
</UL>

<P><STRONG><a name="[14c]"></a>create_xdir</STRONG> (Thumb, 124 bytes, Stack size 40 bytes, ff.o(i.create_xdir))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = create_xdir &rArr; xname_sum
</UL>
<BR>[Calls]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xname_sum
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_word
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[151]"></a>dir_alloc</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, ff.o(i.dir_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 384 + Unknown Stack Size
<LI>Call Chain = dir_alloc &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>
<BR>[Called By]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[154]"></a>dir_find</STRONG> (Thumb, 316 bytes, Stack size 40 bytes, ff.o(i.dir_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 456 + Unknown Stack Size
<LI>Call Chain = dir_find &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xname_sum
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sum_sfn
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cmp
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmp_lfn
</UL>
<BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[153]"></a>dir_next</STRONG> (Thumb, 260 bytes, Stack size 32 bytes, ff.o(i.dir_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 360 + Unknown Stack Size
<LI>Call Chain = dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
</UL>
<BR>[Called By]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;store_xdir
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_xdir
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_remove
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_alloc
</UL>

<P><STRONG><a name="[155]"></a>dir_read</STRONG> (Thumb, 230 bytes, Stack size 32 bytes, ff.o(i.dir_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 416 + Unknown Stack Size
<LI>Call Chain = dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sum_sfn
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pick_lfn
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_xdir
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getlabel
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[15b]"></a>dir_register</STRONG> (Thumb, 566 bytes, Stack size 120 bytes, ff.o(i.dir_register))
<BR><BR>[Stack]<UL><LI>Max Depth = 576 + Unknown Stack Size
<LI>Call Chain = dir_register &rArr; dir_find &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sum_sfn
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;store_xdir
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_word
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_qword
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_obj_dir
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gen_numname
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fill_fat_chain
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_alloc
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_xdir
</UL>
<BR>[Called By]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[162]"></a>dir_remove</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, ff.o(i.dir_remove))
<BR><BR>[Stack]<UL><LI>Max Depth = 384 + Unknown Stack Size
<LI>Call Chain = dir_remove &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
</UL>

<P><STRONG><a name="[152]"></a>dir_sdi</STRONG> (Thumb, 150 bytes, Stack size 24 bytes, ff.o(i.dir_sdi))
<BR><BR>[Stack]<UL><LI>Max Depth = 312 + Unknown Stack Size
<LI>Call Chain = dir_sdi &rArr; get_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getlabel
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;store_xdir
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_obj_dir
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_remove
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_alloc
</UL>

<P><STRONG><a name="[15e]"></a>fill_fat_chain</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, ff.o(i.fill_fat_chain))
<BR><BR>[Stack]<UL><LI>Max Depth = 312 + Unknown Stack Size
<LI>Call Chain = fill_fat_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[146]"></a>find_bitmap</STRONG> (Thumb, 146 bytes, Stack size 32 bytes, ff.o(i.find_bitmap))
<BR><BR>[Stack]<UL><LI>Max Depth = 296 + Unknown Stack Size
<LI>Call Chain = find_bitmap &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>

<P><STRONG><a name="[16d]"></a>find_volume</STRONG> (Thumb, 834 bytes, Stack size 56 bytes, ff.o(i.find_volume))
<BR><BR>[Stack]<UL><LI>Max Depth = 336 + Unknown Stack Size
<LI>Call Chain = find_volume &rArr; check_fs &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_qword
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ldnumber
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getlabel
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
</UL>

<P><STRONG><a name="[174]"></a>follow_path</STRONG> (Thumb, 180 bytes, Stack size 24 bytes, ff.o(i.follow_path))
<BR><BR>[Stack]<UL><LI>Max Depth = 480 + Unknown Stack Size
<LI>Call Chain = follow_path &rArr; dir_find &rArr; dir_read &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_qword
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_clust
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[15d]"></a>gen_numname</STRONG> (Thumb, 134 bytes, Stack size 24 bytes, ff.o(i.gen_numname))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = gen_numname
</UL>
<BR>[Calls]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[145]"></a>get_fat</STRONG> (Thumb, 294 bytes, Stack size 24 bytes, ff.o(i.get_fat))
<BR><BR>[Stack]<UL><LI>Max Depth = 288 + Unknown Stack Size
<LI>Call Chain = get_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>

<P><STRONG><a name="[188]"></a>get_fileinfo</STRONG> (Thumb, 262 bytes, Stack size 32 bytes, ff.o(i.get_fileinfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = get_fileinfo &rArr; get_xdir_info &rArr; ff_convert &rArr; W25QXX_Read &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_xdir_info
</UL>
<BR>[Called By]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
</UL>

<P><STRONG><a name="[17c]"></a>get_ldnumber</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, ff.o(i.get_ldnumber))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = get_ldnumber
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[190]"></a>get_xdir_info</STRONG> (Thumb, 138 bytes, Stack size 24 bytes, ff.o(i.get_xdir_info))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = get_xdir_info &rArr; ff_convert &rArr; W25QXX_Read &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_qword
</UL>
<BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fileinfo
</UL>

<P><STRONG><a name="[180]"></a>ld_clust</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, ff.o(i.ld_clust))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ld_clust
</UL>
<BR>[Calls]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
</UL>

<P><STRONG><a name="[13e]"></a>ld_dword</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, ff.o(i.ld_dword))
<BR><BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getlabel
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fileinfo
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
</UL>

<P><STRONG><a name="[181]"></a>ld_qword</STRONG> (Thumb, 86 bytes, Stack size 8 bytes, ff.o(i.ld_qword))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ld_qword
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_xdir_info
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[13d]"></a>ld_word</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, ff.o(i.ld_word))
<BR><BR>[Called By]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getlabel
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pick_lfn
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_xdir
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_clust
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_xdir_info
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fileinfo
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmp_lfn
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
</UL>

<P><STRONG><a name="[15f]"></a>load_obj_dir</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, ff.o(i.load_obj_dir))
<BR><BR>[Stack]<UL><LI>Max Depth = 392 + Unknown Stack Size
<LI>Call Chain = load_obj_dir &rArr; load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_xdir
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
</UL>
<BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[159]"></a>load_xdir</STRONG> (Thumb, 170 bytes, Stack size 24 bytes, ff.o(i.load_xdir))
<BR><BR>[Stack]<UL><LI>Max Depth = 384 + Unknown Stack Size
<LI>Call Chain = load_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xdir_sum
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_obj_dir
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
</UL>

<P><STRONG><a name="[13f]"></a>mem_cmp</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ff.o(i.mem_cmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = mem_cmp
</UL>
<BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
</UL>

<P><STRONG><a name="[15c]"></a>mem_cpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, ff.o(i.mem_cpy))
<BR><BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;store_xdir
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_xdir
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gen_numname
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[14b]"></a>mem_set</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ff.o(i.mem_set))
<BR><BR>[Called By]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_xdir
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[13b]"></a>move_window</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, ff.o(i.move_window))
<BR><BR>[Stack]<UL><LI>Max Depth = 264 + Unknown Stack Size
<LI>Call Chain = move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
</UL>
<BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getlabel
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;store_xdir
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_xdir
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_bitmap
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_remove
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_alloc
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;change_bitmap
</UL>

<P><STRONG><a name="[15a]"></a>pick_lfn</STRONG> (Thumb, 108 bytes, Stack size 32 bytes, ff.o(i.pick_lfn))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = pick_lfn
</UL>
<BR>[Calls]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
</UL>

<P><STRONG><a name="[147]"></a>put_fat</STRONG> (Thumb, 246 bytes, Stack size 32 bytes, ff.o(i.put_fat))
<BR><BR>[Stack]<UL><LI>Max Depth = 296 + Unknown Stack Size
<LI>Call Chain = put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_word
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_dword
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
</UL>
<BR>[Called By]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fill_fat_chain
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>

<P><STRONG><a name="[184]"></a>putc_bfd</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, ff.o(i.putc_bfd))
<BR><BR>[Stack]<UL><LI>Max Depth = 392 + Unknown Stack Size
<LI>Call Chain = putc_bfd &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_puts
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_putc
</UL>

<P><STRONG><a name="[178]"></a>remove_chain</STRONG> (Thumb, 232 bytes, Stack size 32 bytes, ff.o(i.remove_chain))
<BR><BR>[Stack]<UL><LI>Max Depth = 328 + Unknown Stack Size
<LI>Call Chain = remove_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;change_bitmap
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[177]"></a>st_clust</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, ff.o(i.st_clust))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = st_clust
</UL>
<BR>[Calls]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_word
</UL>
<BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[176]"></a>st_dword</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, ff.o(i.st_dword))
<BR><BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
</UL>

<P><STRONG><a name="[160]"></a>st_qword</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, ff.o(i.st_qword))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = st_qword
</UL>
<BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[14e]"></a>st_word</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ff.o(i.st_word))
<BR><BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;store_xdir
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_clust
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_xdir
</UL>

<P><STRONG><a name="[161]"></a>store_xdir</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, ff.o(i.store_xdir))
<BR><BR>[Stack]<UL><LI>Max Depth = 384 + Unknown Stack Size
<LI>Call Chain = store_xdir &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xdir_sum
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_word
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>
<BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[156]"></a>sum_sfn</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, ff.o(i.sum_sfn))
<BR><BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[17a]"></a>sync_fs</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, ff.o(i.sync_fs))
<BR><BR>[Stack]<UL><LI>Max Depth = 264 + Unknown Stack Size
<LI>Call Chain = sync_fs &rArr; sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_ioctl
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_word
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_dword
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_setlabel
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_rename
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[157]"></a>sync_window</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, ff.o(i.sync_window))
<BR><BR>[Stack]<UL><LI>Max Depth = 248 + Unknown Stack Size
<LI>Call Chain = sync_window &rArr; disk_write &rArr; W25QXX_Write &rArr; W25QXX_Write_NoCheck &rArr; W25QXX_Write_Page &rArr; W25QXX_Wait_Busy &rArr; W25QXX_ReadSR &rArr; SPI1_ReadWriteByte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_CheckFlag_BSY &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>
<BR>[Called By]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>

<P><STRONG><a name="[16a]"></a>validate</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, ff.o(i.validate))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = validate
</UL>
<BR>[Calls]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
</UL>
<BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_closedir
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
</UL>

<P><STRONG><a name="[191]"></a>xdir_sum</STRONG> (Thumb, 52 bytes, Stack size 12 bytes, ff.o(i.xdir_sum))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = xdir_sum
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;store_xdir
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_xdir
</UL>

<P><STRONG><a name="[14d]"></a>xname_sum</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, ff.o(i.xname_sum))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xname_sum
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
</UL>
<BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_xdir
</UL>

<P><STRONG><a name="[80]"></a>_printf_input_char</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_char_common.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_common.o(.text)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
