#include "uart_ext.h"
#include "delay.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

// 全局UART扩展数据
UartExt_t g_uart_ext;

// 调试级别名称
static const char* debug_level_names[] = {
    "NONE",
    "ERROR",
    "WARNING", 
    "INFO",
    "DEBUG",
    "VERBOSE"
};

// 调试级别颜色
static const char* debug_level_colors[] = {
    ANSI_COLOR_WHITE,
    ANSI_COLOR_RED,
    ANSI_COLOR_YELLOW,
    ANSI_COLOR_GREEN,
    ANSI_COLOR_BLUE,
    ANSI_COLOR_CYAN
};

// 命令表
#define MAX_COMMANDS 16
static Command_t command_table[MAX_COMMANDS];
static u8 command_count = 0;

/**
 * @brief UART扩展功能初始化
 */
void UartExt_Init(void)
{
    // 初始化数据结构
    g_uart_ext.current_level = DEBUG_LEVEL_INFO;
    g_uart_ext.timestamp_enabled = UART_EXT_TIMESTAMP;
    g_uart_ext.color_enabled = UART_EXT_COLOR_OUTPUT;
    g_uart_ext.tx_count = 0;
    g_uart_ext.rx_count = 0;
    
    // 注册内置命令
    UartExt_RegisterCommand("help", Cmd_Help, "Show available commands");
    UartExt_RegisterCommand("clear", Cmd_Clear, "Clear screen");
    UartExt_RegisterCommand("reset", Cmd_Reset, "Reset system");
    UartExt_RegisterCommand("info", Cmd_Info, "Show system information");
    UartExt_RegisterCommand("debug", Cmd_Debug, "Set debug level");
    
    printf("UART Extended: Initialized successfully\r\n");
}

/**
 * @brief 设置调试级别
 * @param level: 调试级别
 */
void UartExt_SetDebugLevel(DebugLevel_t level)
{
    if (level < DEBUG_LEVEL_VERBOSE + 1) {
        g_uart_ext.current_level = level;
        printf("Debug level set to: %s\r\n", debug_level_names[level]);
    }
}

/**
 * @brief 获取当前调试级别
 * @return 当前调试级别
 */
DebugLevel_t UartExt_GetDebugLevel(void)
{
    return g_uart_ext.current_level;
}

/**
 * @brief 启用/禁用时间戳
 * @param enable: 1-启用, 0-禁用
 */
void UartExt_EnableTimestamp(u8 enable)
{
    g_uart_ext.timestamp_enabled = enable;
}

/**
 * @brief 启用/禁用彩色输出
 * @param enable: 1-启用, 0-禁用
 */
void UartExt_EnableColor(u8 enable)
{
    g_uart_ext.color_enabled = enable;
}

/**
 * @brief 格式化输出函数
 * @param format: 格式字符串
 * @param ...: 可变参数
 */
void UartExt_Printf(const char* format, ...)
{
    va_list args;
    int len;
    
    va_start(args, format);
    len = vsnprintf(g_uart_ext.tx_buffer, UART_EXT_BUFFER_SIZE, format, args);
    va_end(args);
    
    if (len > 0) {
        printf("%s", g_uart_ext.tx_buffer);
        g_uart_ext.tx_count += len;
    }
}

/**
 * @brief 带级别的格式化输出函数
 * @param level: 调试级别
 * @param format: 格式字符串
 * @param ...: 可变参数
 */
void UartExt_PrintfLevel(DebugLevel_t level, const char* format, ...)
{
    va_list args;
    int len;
    u32 timestamp;
    
    // 检查调试级别
    if (level > g_uart_ext.current_level) {
        return;
    }
    
    // 输出时间戳
    if (g_uart_ext.timestamp_enabled) {
        timestamp = HAL_GetTick();
        printf("[%08d] ", timestamp);
    }
    
    // 输出级别标识
    if (g_uart_ext.color_enabled) {
        printf("%s[%s]%s ", debug_level_colors[level], debug_level_names[level], ANSI_COLOR_RESET);
    } else {
        printf("[%s] ", debug_level_names[level]);
    }
    
    // 输出格式化内容
    va_start(args, format);
    len = vsnprintf(g_uart_ext.tx_buffer, UART_EXT_BUFFER_SIZE, format, args);
    va_end(args);
    
    if (len > 0) {
        printf("%s", g_uart_ext.tx_buffer);
        g_uart_ext.tx_count += len;
    }
}

/**
 * @brief 打印十六进制数据
 * @param data: 数据指针
 * @param length: 数据长度
 */
void UartExt_PrintHex(const u8* data, u16 length)
{
    printf("Hex Data (%d bytes): ", length);
    for (u16 i = 0; i < length; i++) {
        printf("%02X ", data[i]);
        if ((i + 1) % 16 == 0) {
            printf("\r\n");
        }
    }
    if (length % 16 != 0) {
        printf("\r\n");
    }
}

/**
 * @brief 打印二进制数据
 * @param value: 数值
 * @param bits: 位数
 */
void UartExt_PrintBinary(u32 value, u8 bits)
{
    printf("Binary: ");
    for (int i = bits - 1; i >= 0; i--) {
        printf("%d", (value >> i) & 1);
        if (i % 4 == 0 && i > 0) {
            printf(" ");
        }
    }
    printf("\r\n");
}

/**
 * @brief 打印浮点数
 * @param value: 浮点值
 * @param decimals: 小数位数
 */
void UartExt_PrintFloat(float value, u8 decimals)
{
    char format[16];
    snprintf(format, sizeof(format), "%%.%df", decimals);
    printf(format, value);
}

/**
 * @brief 打印系统信息
 */
void UartExt_PrintSystemInfo(void)
{
    printf("\r\n=== System Information ===\r\n");
    printf("MCU: STM32F407ZGT6\r\n");
    printf("Core Clock: %d MHz\r\n", SystemCoreClock / 1000000);
    printf("System Tick: %d ms\r\n", HAL_GetTick());
    printf("Debug Level: %s\r\n", debug_level_names[g_uart_ext.current_level]);
    printf("TX Count: %d bytes\r\n", g_uart_ext.tx_count);
    printf("RX Count: %d bytes\r\n", g_uart_ext.rx_count);
    printf("==========================\r\n\r\n");
}

/**
 * @brief 获取发送字节计数
 * @return 发送字节数
 */
u32 UartExt_GetTxCount(void)
{
    return g_uart_ext.tx_count;
}

/**
 * @brief 获取接收字节计数
 * @return 接收字节数
 */
u32 UartExt_GetRxCount(void)
{
    return g_uart_ext.rx_count;
}

/**
 * @brief 重置计数器
 */
void UartExt_ResetCounters(void)
{
    g_uart_ext.tx_count = 0;
    g_uart_ext.rx_count = 0;
}

/**
 * @brief 注册命令
 * @param command: 命令字符串
 * @param handler: 命令处理函数
 * @param description: 命令描述
 */
void UartExt_RegisterCommand(const char* command, CommandHandler_t handler, const char* description)
{
    if (command_count < MAX_COMMANDS) {
        command_table[command_count].command = command;
        command_table[command_count].handler = handler;
        command_table[command_count].description = description;
        command_count++;
    }
}

/**
 * @brief 显示帮助信息
 */
void UartExt_ShowHelp(void)
{
    printf("\r\n=== Available Commands ===\r\n");
    for (u8 i = 0; i < command_count; i++) {
        printf("%-10s - %s\r\n", command_table[i].command, command_table[i].description);
    }
    printf("==========================\r\n\r\n");
}

// ==================== 内置命令处理函数 ====================

/**
 * @brief 帮助命令处理函数
 */
void Cmd_Help(int argc, char* argv[])
{
    UartExt_ShowHelp();
}

/**
 * @brief 清屏命令处理函数
 */
void Cmd_Clear(int argc, char* argv[])
{
    printf("\033[2J\033[H"); // ANSI清屏命令
}

/**
 * @brief 重置命令处理函数
 */
void Cmd_Reset(int argc, char* argv[])
{
    printf("System resetting...\r\n");
    HAL_Delay(100);
    NVIC_SystemReset();
}

/**
 * @brief 信息命令处理函数
 */
void Cmd_Info(int argc, char* argv[])
{
    UartExt_PrintSystemInfo();
}

/**
 * @brief 调试级别设置命令处理函数
 */
void Cmd_Debug(int argc, char* argv[])
{
    if (argc < 2) {
        printf("Current debug level: %s\r\n", debug_level_names[g_uart_ext.current_level]);
        printf("Usage: debug <level> (0-5)\r\n");
        return;
    }
    
    int level = atoi(argv[1]);
    if (level >= 0 && level <= DEBUG_LEVEL_VERBOSE) {
        UartExt_SetDebugLevel((DebugLevel_t)level);
    } else {
        printf("Invalid debug level. Range: 0-5\r\n");
    }
}
