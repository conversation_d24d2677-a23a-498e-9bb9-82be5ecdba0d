#ifndef __STATE_MACHINE_H
#define __STATE_MACHINE_H

#include "sys.h"

// 状态机最大状态数量
#define MAX_STATES 16

// 状态机事件类型
typedef enum {
    EVENT_NONE = 0,
    EVENT_INIT,
    EVENT_START,
    EVENT_STOP,
    EVENT_TIMEOUT,
    EVENT_KEY_PRESS,
    EVENT_SENSOR_DATA,
    EVENT_ERROR,
    EVENT_RESET,
    EVENT_MAX
} StateMachineEvent_t;

// 状态机状态类型
typedef enum {
    STATE_IDLE = 0,
    STATE_INIT,
    STATE_RUNNING,
    STATE_MEASURING,
    STATE_DISPLAYING,
    STATE_ERROR,
    STATE_SLEEP,
    STATE_MAX
} StateMachineState_t;

// 状态机结构体
typedef struct {
    StateMachineState_t current_state;      // 当前状态
    StateMachineState_t previous_state;     // 前一个状态
    StateMachineEvent_t current_event;      // 当前事件
    u32 state_timer;                        // 状态计时器
    u32 state_timeout;                      // 状态超时时间
    u8 state_changed;                       // 状态改变标志
} StateMachine_t;

// 状态处理函数指针类型
typedef StateMachineState_t (*StateHandler_t)(StateMachine_t* sm, StateMachineEvent_t event);

// 状态机函数声明
void StateMachine_Init(StateMachine_t* sm);
void StateMachine_SetState(StateMachine_t* sm, StateMachineState_t new_state);
void StateMachine_SendEvent(StateMachine_t* sm, StateMachineEvent_t event);
void StateMachine_Process(StateMachine_t* sm);
StateMachineState_t StateMachine_GetCurrentState(StateMachine_t* sm);
u8 StateMachine_IsStateChanged(StateMachine_t* sm);
void StateMachine_SetTimeout(StateMachine_t* sm, u32 timeout_ms);
u8 StateMachine_IsTimeout(StateMachine_t* sm);

// 状态处理函数声明
StateMachineState_t State_Idle_Handler(StateMachine_t* sm, StateMachineEvent_t event);
StateMachineState_t State_Init_Handler(StateMachine_t* sm, StateMachineEvent_t event);
StateMachineState_t State_Running_Handler(StateMachine_t* sm, StateMachineEvent_t event);
StateMachineState_t State_Measuring_Handler(StateMachine_t* sm, StateMachineEvent_t event);
StateMachineState_t State_Displaying_Handler(StateMachine_t* sm, StateMachineEvent_t event);
StateMachineState_t State_Error_Handler(StateMachine_t* sm, StateMachineEvent_t event);
StateMachineState_t State_Sleep_Handler(StateMachine_t* sm, StateMachineEvent_t event);

// 全局状态机实例
extern StateMachine_t g_state_machine;

#endif /* __STATE_MACHINE_H */
