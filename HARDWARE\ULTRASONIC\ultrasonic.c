#include "ultrasonic.h"
#include "delay.h"
#include "usart.h"

// 全局超声波数据
UltrasonicData_t g_ultrasonic_data;

/**
 * @brief 超声波传感器初始化
 */
void Ultrasonic_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 使能GPIO时钟
    ULTRASONIC_TRIG_CLK();
    ULTRASONIC_ECHO_CLK();
    
    // 配置TRIG引脚为输出
    GPIO_InitStruct.Pin = ULTRASONIC_TRIG_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(ULTRASONIC_TRIG_PORT, &GPIO_InitStruct);
    
    // 配置ECHO引脚为输入
    GPIO_InitStruct.Pin = ULTRASONIC_ECHO_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLDOWN;
    HAL_GPIO_Init(ULTRASONIC_ECHO_PORT, &GPIO_InitStruct);
    
    // 初始化TRIG引脚为低电平
    HAL_GPIO_WritePin(ULTRASONIC_TRIG_PORT, ULTRASONIC_TRIG_PIN, GPIO_PIN_RESET);
    
    // 初始化数据结构
    g_ultrasonic_data.distance_cm = 0.0f;
    g_ultrasonic_data.distance_mm = 0.0f;
    g_ultrasonic_data.echo_time_us = 0;
    g_ultrasonic_data.status = ULTRASONIC_OK;
    g_ultrasonic_data.last_measure_time = 0;
    g_ultrasonic_data.is_measuring = 0;
    
    printf("Ultrasonic: Sensor initialized\r\n");
}

/**
 * @brief 触发超声波脉冲
 */
static void Ultrasonic_TriggerPulse(void)
{
    // 发送10us的高电平脉冲
    HAL_GPIO_WritePin(ULTRASONIC_TRIG_PORT, ULTRASONIC_TRIG_PIN, GPIO_PIN_SET);
    delay_us(10);
    HAL_GPIO_WritePin(ULTRASONIC_TRIG_PORT, ULTRASONIC_TRIG_PIN, GPIO_PIN_RESET);
}

/**
 * @brief 测量回波时间
 * @return 回波时间(微秒)，0表示超时
 */
static u32 Ultrasonic_MeasureEchoTime(void)
{
    u32 start_time, end_time;
    u32 timeout_counter = 0;
    
    // 等待ECHO引脚变高
    while (HAL_GPIO_ReadPin(ULTRASONIC_ECHO_PORT, ULTRASONIC_ECHO_PIN) == GPIO_PIN_RESET) {
        delay_us(1);
        timeout_counter++;
        if (timeout_counter > ULTRASONIC_TIMEOUT) {
            return 0; // 超时
        }
    }
    
    // 记录开始时间
    start_time = HAL_GetTick() * 1000 + (SysTick->LOAD - SysTick->VAL) / (SystemCoreClock / 1000000);
    
    // 等待ECHO引脚变低
    timeout_counter = 0;
    while (HAL_GPIO_ReadPin(ULTRASONIC_ECHO_PORT, ULTRASONIC_ECHO_PIN) == GPIO_PIN_SET) {
        delay_us(1);
        timeout_counter++;
        if (timeout_counter > ULTRASONIC_TIMEOUT) {
            return 0; // 超时
        }
    }
    
    // 记录结束时间
    end_time = HAL_GetTick() * 1000 + (SysTick->LOAD - SysTick->VAL) / (SystemCoreClock / 1000000);
    
    return (end_time - start_time);
}

/**
 * @brief 根据回波时间计算距离
 * @param echo_time_us: 回波时间(微秒)
 * @return 距离(厘米)
 */
static float Ultrasonic_CalculateDistance(u32 echo_time_us)
{
    // 距离 = (回波时间 * 声速) / 2
    // 声速 = 340m/s = 0.034cm/us
    float distance = (float)echo_time_us * 0.034f / 2.0f;
    return distance;
}

/**
 * @brief 执行一次完整的超声波测量
 * @return 测量状态
 */
UltrasonicStatus_t Ultrasonic_Measure(void)
{
    u32 echo_time;
    float distance;
    
    // 触发测量
    Ultrasonic_TriggerPulse();
    
    // 测量回波时间
    echo_time = Ultrasonic_MeasureEchoTime();
    
    if (echo_time == 0) {
        g_ultrasonic_data.status = ULTRASONIC_TIMEOUT;
        printf("Ultrasonic: Measurement timeout\r\n");
        return ULTRASONIC_TIMEOUT;
    }
    
    // 计算距离
    distance = Ultrasonic_CalculateDistance(echo_time);
    
    // 检查距离范围
    if (distance < ULTRASONIC_MIN_DISTANCE || distance > ULTRASONIC_MAX_DISTANCE) {
        g_ultrasonic_data.status = ULTRASONIC_OUT_OF_RANGE;
        printf("Ultrasonic: Distance out of range: %.2fcm\r\n", distance);
        return ULTRASONIC_OUT_OF_RANGE;
    }
    
    // 更新数据
    g_ultrasonic_data.distance_cm = distance;
    g_ultrasonic_data.distance_mm = distance * 10.0f;
    g_ultrasonic_data.echo_time_us = echo_time;
    g_ultrasonic_data.status = ULTRASONIC_OK;
    g_ultrasonic_data.last_measure_time = HAL_GetTick();
    
    printf("Ultrasonic: Distance = %.2fcm, Echo time = %dus\r\n", distance, echo_time);
    
    return ULTRASONIC_OK;
}

/**
 * @brief 开始异步测量
 */
void Ultrasonic_StartMeasure(void)
{
    if (!g_ultrasonic_data.is_measuring) {
        g_ultrasonic_data.is_measuring = 1;
        Ultrasonic_TriggerPulse();
        g_ultrasonic_data.last_measure_time = HAL_GetTick();
    }
}

/**
 * @brief 检查异步测量是否完成
 * @return 1: 完成, 0: 未完成
 */
u8 Ultrasonic_IsMeasureComplete(void)
{
    if (!g_ultrasonic_data.is_measuring) {
        return 1;
    }
    
    // 检查是否超时
    if ((HAL_GetTick() - g_ultrasonic_data.last_measure_time) > 100) {
        g_ultrasonic_data.is_measuring = 0;
        g_ultrasonic_data.status = ULTRASONIC_TIMEOUT;
        return 1;
    }
    
    return 0;
}

/**
 * @brief 处理异步测量
 */
void Ultrasonic_ProcessMeasure(void)
{
    if (g_ultrasonic_data.is_measuring) {
        // 这里可以添加中断方式的处理逻辑
        // 目前使用简单的轮询方式
        if (Ultrasonic_IsMeasureComplete()) {
            g_ultrasonic_data.is_measuring = 0;
        }
    }
}

/**
 * @brief 获取距离(厘米)
 * @return 距离值
 */
float Ultrasonic_GetDistance_CM(void)
{
    return g_ultrasonic_data.distance_cm;
}

/**
 * @brief 获取距离(毫米)
 * @return 距离值
 */
float Ultrasonic_GetDistance_MM(void)
{
    return g_ultrasonic_data.distance_mm;
}

/**
 * @brief 获取传感器状态
 * @return 传感器状态
 */
UltrasonicStatus_t Ultrasonic_GetStatus(void)
{
    return g_ultrasonic_data.status;
}
