#include "state_machine.h"
#include "delay.h"
#include "usart.h"

// 全局状态机实例
StateMachine_t g_state_machine;

// 状态处理函数表
static StateHandler_t state_handlers[STATE_MAX] = {
    State_Idle_Handler,         // STATE_IDLE
    State_Init_Handler,         // STATE_INIT
    State_Running_Handler,      // STATE_RUNNING
    State_Measuring_Handler,    // STATE_MEASURING
    State_Displaying_Handler,   // STATE_DISPLAYING
    State_Error_Handler,        // STATE_ERROR
    State_Sleep_Handler         // STATE_SLEEP
};

// 状态名称字符串（用于调试）
static const char* state_names[STATE_MAX] = {
    "IDLE",
    "INIT",
    "RUNNING",
    "MEASURING",
    "DISPLAYING",
    "ERROR",
    "SLEEP"
};

// 事件名称字符串（用于调试）
static const char* event_names[EVENT_MAX] = {
    "NONE",
    "INIT",
    "START",
    "STOP",
    "TIMEOUT",
    "KEY_PRESS",
    "SENSOR_DATA",
    "ERROR",
    "RESET"
};

/**
 * @brief 初始化状态机
 * @param sm: 状态机指针
 */
void StateMachine_Init(StateMachine_t* sm)
{
    if (sm == NULL) return;
    
    sm->current_state = STATE_IDLE;
    sm->previous_state = STATE_IDLE;
    sm->current_event = EVENT_NONE;
    sm->state_timer = 0;
    sm->state_timeout = 0;
    sm->state_changed = 1;
    
    printf("StateMachine: Initialized to IDLE state\r\n");
}

/**
 * @brief 设置状态机状态
 * @param sm: 状态机指针
 * @param new_state: 新状态
 */
void StateMachine_SetState(StateMachine_t* sm, StateMachineState_t new_state)
{
    if (sm == NULL || new_state >= STATE_MAX) return;
    
    if (sm->current_state != new_state) {
        sm->previous_state = sm->current_state;
        sm->current_state = new_state;
        sm->state_timer = HAL_GetTick();
        sm->state_changed = 1;
        
        printf("StateMachine: State changed from %s to %s\r\n", 
               state_names[sm->previous_state], 
               state_names[sm->current_state]);
    }
}

/**
 * @brief 向状态机发送事件
 * @param sm: 状态机指针
 * @param event: 事件类型
 */
void StateMachine_SendEvent(StateMachine_t* sm, StateMachineEvent_t event)
{
    if (sm == NULL || event >= EVENT_MAX) return;
    
    sm->current_event = event;
    printf("StateMachine: Event %s sent to state %s\r\n", 
           event_names[event], 
           state_names[sm->current_state]);
}

/**
 * @brief 处理状态机
 * @param sm: 状态机指针
 */
void StateMachine_Process(StateMachine_t* sm)
{
    if (sm == NULL || sm->current_state >= STATE_MAX) return;
    
    // 调用当前状态的处理函数
    StateHandler_t handler = state_handlers[sm->current_state];
    if (handler != NULL) {
        StateMachineState_t next_state = handler(sm, sm->current_event);
        
        // 如果返回的状态与当前状态不同，则切换状态
        if (next_state != sm->current_state && next_state < STATE_MAX) {
            StateMachine_SetState(sm, next_state);
        }
    }
    
    // 清除事件
    sm->current_event = EVENT_NONE;
    sm->state_changed = 0;
}

/**
 * @brief 获取当前状态
 * @param sm: 状态机指针
 * @return 当前状态
 */
StateMachineState_t StateMachine_GetCurrentState(StateMachine_t* sm)
{
    if (sm == NULL) return STATE_IDLE;
    return sm->current_state;
}

/**
 * @brief 检查状态是否改变
 * @param sm: 状态机指针
 * @return 1: 状态已改变, 0: 状态未改变
 */
u8 StateMachine_IsStateChanged(StateMachine_t* sm)
{
    if (sm == NULL) return 0;
    return sm->state_changed;
}

/**
 * @brief 设置状态超时时间
 * @param sm: 状态机指针
 * @param timeout_ms: 超时时间（毫秒）
 */
void StateMachine_SetTimeout(StateMachine_t* sm, u32 timeout_ms)
{
    if (sm == NULL) return;
    sm->state_timeout = timeout_ms;
}

/**
 * @brief 检查状态是否超时
 * @param sm: 状态机指针
 * @return 1: 已超时, 0: 未超时
 */
u8 StateMachine_IsTimeout(StateMachine_t* sm)
{
    if (sm == NULL || sm->state_timeout == 0) return 0;
    
    u32 current_time = HAL_GetTick();
    return (current_time - sm->state_timer) >= sm->state_timeout;
}

// ==================== 状态处理函数实现 ====================

/**
 * @brief 空闲状态处理函数
 */
StateMachineState_t State_Idle_Handler(StateMachine_t* sm, StateMachineEvent_t event)
{
    switch (event) {
        case EVENT_INIT:
            return STATE_INIT;

        case EVENT_START:
            return STATE_RUNNING;

        case EVENT_KEY_PRESS:
            return STATE_INIT;

        default:
            return STATE_IDLE;
    }
}

/**
 * @brief 初始化状态处理函数
 */
StateMachineState_t State_Init_Handler(StateMachine_t* sm, StateMachineEvent_t event)
{
    static u8 init_step = 0;

    if (sm->state_changed) {
        init_step = 0;
        printf("StateMachine: Starting initialization...\r\n");
        StateMachine_SetTimeout(sm, 5000); // 5秒超时
    }

    switch (event) {
        case EVENT_NONE:
            // 执行初始化步骤
            init_step++;
            if (init_step >= 3) {
                printf("StateMachine: Initialization completed\r\n");
                return STATE_RUNNING;
            }
            break;

        case EVENT_TIMEOUT:
            printf("StateMachine: Initialization timeout\r\n");
            return STATE_ERROR;

        case EVENT_ERROR:
            return STATE_ERROR;

        default:
            break;
    }

    return STATE_INIT;
}

/**
 * @brief 运行状态处理函数
 */
StateMachineState_t State_Running_Handler(StateMachine_t* sm, StateMachineEvent_t event)
{
    if (sm->state_changed) {
        printf("StateMachine: System running\r\n");
        StateMachine_SetTimeout(sm, 1000); // 1秒后开始测量
    }

    switch (event) {
        case EVENT_TIMEOUT:
            return STATE_MEASURING;

        case EVENT_STOP:
            return STATE_IDLE;

        case EVENT_ERROR:
            return STATE_ERROR;

        case EVENT_KEY_PRESS:
            return STATE_MEASURING;

        default:
            break;
    }

    return STATE_RUNNING;
}

/**
 * @brief 测量状态处理函数
 */
StateMachineState_t State_Measuring_Handler(StateMachine_t* sm, StateMachineEvent_t event)
{
    if (sm->state_changed) {
        printf("StateMachine: Starting measurement\r\n");
        StateMachine_SetTimeout(sm, 2000); // 2秒测量超时
    }

    switch (event) {
        case EVENT_SENSOR_DATA:
            printf("StateMachine: Sensor data received\r\n");
            return STATE_DISPLAYING;

        case EVENT_TIMEOUT:
            printf("StateMachine: Measurement timeout\r\n");
            return STATE_RUNNING;

        case EVENT_ERROR:
            return STATE_ERROR;

        case EVENT_STOP:
            return STATE_IDLE;

        default:
            break;
    }

    return STATE_MEASURING;
}

/**
 * @brief 显示状态处理函数
 */
StateMachineState_t State_Displaying_Handler(StateMachine_t* sm, StateMachineEvent_t event)
{
    if (sm->state_changed) {
        printf("StateMachine: Displaying data\r\n");
        StateMachine_SetTimeout(sm, 3000); // 3秒显示时间
    }

    switch (event) {
        case EVENT_TIMEOUT:
            return STATE_RUNNING;

        case EVENT_KEY_PRESS:
            return STATE_MEASURING;

        case EVENT_STOP:
            return STATE_IDLE;

        case EVENT_ERROR:
            return STATE_ERROR;

        default:
            break;
    }

    return STATE_DISPLAYING;
}

/**
 * @brief 错误状态处理函数
 */
StateMachineState_t State_Error_Handler(StateMachine_t* sm, StateMachineEvent_t event)
{
    if (sm->state_changed) {
        printf("StateMachine: Error state entered\r\n");
        StateMachine_SetTimeout(sm, 5000); // 5秒后自动重置
    }

    switch (event) {
        case EVENT_RESET:
        case EVENT_TIMEOUT:
            printf("StateMachine: Resetting from error state\r\n");
            return STATE_IDLE;

        case EVENT_KEY_PRESS:
            return STATE_IDLE;

        default:
            break;
    }

    return STATE_ERROR;
}

/**
 * @brief 睡眠状态处理函数
 */
StateMachineState_t State_Sleep_Handler(StateMachine_t* sm, StateMachineEvent_t event)
{
    if (sm->state_changed) {
        printf("StateMachine: Entering sleep mode\r\n");
    }

    switch (event) {
        case EVENT_KEY_PRESS:
        case EVENT_START:
            return STATE_IDLE;

        default:
            break;
    }

    return STATE_SLEEP;
}
