#ifndef __UART_EXT_H
#define __UART_EXT_H

#include "sys.h"
#include "usart.h"
#include <stdarg.h>

// 调试级别定义
typedef enum {
    DEBUG_LEVEL_NONE = 0,
    DEBUG_LEVEL_ERROR,
    DEBUG_LEVEL_WARNING,
    DEBUG_LEVEL_INFO,
    DEBUG_LEVEL_DEBUG,
    DEBUG_LEVEL_VERBOSE
} DebugLevel_t;

// UART扩展功能配置
#define UART_EXT_BUFFER_SIZE    512
#define UART_EXT_MAX_ARGS       10
#define UART_EXT_TIMESTAMP      1       // 启用时间戳
#define UART_EXT_COLOR_OUTPUT   1       // 启用彩色输出

// ANSI颜色代码
#define ANSI_COLOR_RED          "\x1b[31m"
#define ANSI_COLOR_GREEN        "\x1b[32m"
#define ANSI_COLOR_YELLOW       "\x1b[33m"
#define ANSI_COLOR_BLUE         "\x1b[34m"
#define ANSI_COLOR_MAGENTA      "\x1b[35m"
#define ANSI_COLOR_CYAN         "\x1b[36m"
#define ANSI_COLOR_WHITE        "\x1b[37m"
#define ANSI_COLOR_RESET        "\x1b[0m"

// UART扩展数据结构
typedef struct {
    DebugLevel_t current_level;     // 当前调试级别
    u8 timestamp_enabled;           // 时间戳使能
    u8 color_enabled;               // 彩色输出使能
    u32 tx_count;                   // 发送字节计数
    u32 rx_count;                   // 接收字节计数
    char tx_buffer[UART_EXT_BUFFER_SIZE];  // 发送缓冲区
} UartExt_t;

// 函数声明
void UartExt_Init(void);
void UartExt_SetDebugLevel(DebugLevel_t level);
DebugLevel_t UartExt_GetDebugLevel(void);
void UartExt_EnableTimestamp(u8 enable);
void UartExt_EnableColor(u8 enable);

// 格式化输出函数
void UartExt_Printf(const char* format, ...);
void UartExt_PrintfLevel(DebugLevel_t level, const char* format, ...);

// 调试输出宏
#define UART_ERROR(...)     UartExt_PrintfLevel(DEBUG_LEVEL_ERROR, __VA_ARGS__)
#define UART_WARNING(...)   UartExt_PrintfLevel(DEBUG_LEVEL_WARNING, __VA_ARGS__)
#define UART_INFO(...)      UartExt_PrintfLevel(DEBUG_LEVEL_INFO, __VA_ARGS__)
#define UART_DEBUG(...)     UartExt_PrintfLevel(DEBUG_LEVEL_DEBUG, __VA_ARGS__)
#define UART_VERBOSE(...)   UartExt_PrintfLevel(DEBUG_LEVEL_VERBOSE, __VA_ARGS__)

// 数据输出函数
void UartExt_PrintHex(const u8* data, u16 length);
void UartExt_PrintBinary(u32 value, u8 bits);
void UartExt_PrintFloat(float value, u8 decimals);

// 系统信息输出
void UartExt_PrintSystemInfo(void);
void UartExt_PrintMemoryInfo(void);
void UartExt_PrintTaskInfo(void);

// 数据统计函数
u32 UartExt_GetTxCount(void);
u32 UartExt_GetRxCount(void);
void UartExt_ResetCounters(void);

// 命令行接口
typedef void (*CommandHandler_t)(int argc, char* argv[]);

typedef struct {
    const char* command;
    CommandHandler_t handler;
    const char* description;
} Command_t;

void UartExt_RegisterCommand(const char* command, CommandHandler_t handler, const char* description);
void UartExt_ProcessCommand(void);
void UartExt_ShowHelp(void);

// 内置命令处理函数
void Cmd_Help(int argc, char* argv[]);
void Cmd_Clear(int argc, char* argv[]);
void Cmd_Reset(int argc, char* argv[]);
void Cmd_Info(int argc, char* argv[]);
void Cmd_Debug(int argc, char* argv[]);

// 全局变量声明
extern UartExt_t g_uart_ext;

#endif /* __UART_EXT_H */
