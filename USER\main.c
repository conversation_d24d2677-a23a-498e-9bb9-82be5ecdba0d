#include "sys.h"
#include "delay.h"
#include "usart.h"
#include "led.h"
#include "key.h"
#include "lcd.h"
#include "usmart.h"
#include "sram.h"
#include "malloc.h"
#include "sdio_sdcard.h"
#include "w25qxx.h"
#include "ff.h"
#include "exfuns.h"
#include "string.h"
#include "usmart.h"
#include "fontupd.h"
#include "text.h"

// 新增模块头文件
#include "state_machine.h"
#include "ultrasonic.h"
#include "adc.h"
#include "uart_ext.h"
#include "display_manager.h"

/************************************************
 STM32F407ZGT6 超声波测距系统
 基于状态机的多功能测量显示系统

 功能特性:
 - 状态机管理系统运行状态
 - HC-SR04超声波测距
 - 4通道ADC数据采集
 - LCD显示测量数据
 - UART调试输出
 - 按键交互控制

 硬件连接:
 - 超声波传感器: PE2(TRIG), PE3(ECHO)
 - ADC通道: PA0-PA3
 - LCD: FSMC接口
 - LED: PF9(LED0), PF10(LED1)
 - 按键: PE4(KEY0), PE3(KEY1), PA0(KEY_UP)

************************************************/

// 全局变量定义
static u32 system_tick_counter = 0;
static u8 key_pressed = 0;
static u8 display_mode_switch = 0;

// 函数声明
static void System_Init(void);
static void System_Task_1ms(void);
static void System_Task_10ms(void);
static void System_Task_100ms(void);
static void System_Task_1000ms(void);
static void Process_KeyInput(void);
static void Process_StateMachine(void);

/**
 * @brief 主函数
 */
int main(void)
{
    // 系统初始化
    System_Init();

    // 启动状态机
    StateMachine_SendEvent(&g_state_machine, EVENT_INIT);

    UART_INFO("=== STM32F407 Ultrasonic System Started ===\r\n");
    UART_INFO("System initialized successfully\r\n");
    UART_INFO("Press KEY0 to start measurement\r\n");
    UART_INFO("Press KEY1 to switch display mode\r\n");
    UART_INFO("Press KEY_UP to reset system\r\n");

    // 主循环
    while(1)
    {
        system_tick_counter++;

        // 1ms任务
        System_Task_1ms();

        // 10ms任务
        if(system_tick_counter % 10 == 0) {
            System_Task_10ms();
        }

        // 100ms任务
        if(system_tick_counter % 100 == 0) {
            System_Task_100ms();
        }

        // 1000ms任务
        if(system_tick_counter % 1000 == 0) {
            System_Task_1000ms();
            system_tick_counter = 0; // 防止溢出
        }

        delay_ms(1); // 1ms基准延时
    }
}

/**
 * @brief 系统初始化
 */
static void System_Init(void)
{
    // HAL库和时钟初始化
    HAL_Init();
    Stm32_Clock_Init(336,8,2,7);  // 设置时钟,168Mhz
    delay_init(168);

    // 基础外设初始化
    uart_init(115200);
    usmart_dev.init(84);
    LED_Init();
    KEY_Init();
    LCD_Init();
    SRAM_Init();
    W25QXX_Init();
    my_mem_init(SRAMIN);
    my_mem_init(SRAMEX);

    // 文件系统初始化
    exfuns_init();
    f_mount(fs[0],"0:",1);
    f_mount(fs[1],"1:",1);

    // 字库初始化(简化版本)
    if(font_init()) {
        UART_WARNING("Font initialization failed, using default font\r\n");
    }

    // 新增模块初始化
    UartExt_Init();
    StateMachine_Init(&g_state_machine);
    Ultrasonic_Init();
    ADC_Init();
    ADC_Calibrate();
    DisplayManager_Init();

    UART_INFO("System initialization completed\r\n");
}

/**
 * @brief 1ms任务
 */
static void System_Task_1ms(void)
{
    // 处理按键输入
    Process_KeyInput();

    // 处理状态机
    Process_StateMachine();

    // 检查状态机超时
    if(StateMachine_IsTimeout(&g_state_machine)) {
        StateMachine_SendEvent(&g_state_machine, EVENT_TIMEOUT);
    }
}

/**
 * @brief 10ms任务
 */
static void System_Task_10ms(void)
{
    // 更新LED状态
    static u16 led_counter = 0;
    led_counter++;

    if(led_counter >= 50) { // 500ms闪烁
        LED0 = !LED0;
        led_counter = 0;
    }

    // 处理超声波测量
    Ultrasonic_ProcessMeasure();

    // 处理ADC转换
    ADC_ProcessConversion();
}

/**
 * @brief 100ms任务
 */
static void System_Task_100ms(void)
{
    // 读取ADC数据
    ADC_ReadAllChannels();

    // 更新显示
    DisplayManager_Update();
}

/**
 * @brief 1000ms任务
 */
static void System_Task_1000ms(void)
{
    // 系统状态指示
    LED1 = !LED1;

    // 输出系统状态信息
    UART_DEBUG("System running, State: %d, Tick: %d\r\n",
               StateMachine_GetCurrentState(&g_state_machine),
               HAL_GetTick());
}

/**
 * @brief 处理按键输入
 */
static void Process_KeyInput(void)
{
    u8 key = KEY_Scan(0);

    if(key != 0) {
        key_pressed = key;
        UART_INFO("Key pressed: %d\r\n", key);

        switch(key) {
            case KEY0_PRES: // 开始/停止测量
                if(StateMachine_GetCurrentState(&g_state_machine) == STATE_IDLE) {
                    StateMachine_SendEvent(&g_state_machine, EVENT_START);
                } else {
                    StateMachine_SendEvent(&g_state_machine, EVENT_KEY_PRESS);
                }
                break;

            case KEY1_PRES: // 切换显示模式
                display_mode_switch++;
                if(display_mode_switch >= DISPLAY_MODE_MAX) {
                    display_mode_switch = 0;
                }
                DisplayManager_SetMode((DisplayMode_t)display_mode_switch);
                break;

            case WKUP_PRES: // 重置系统
                StateMachine_SendEvent(&g_state_machine, EVENT_RESET);
                break;

            default:
                break;
        }
    }
}

/**
 * @brief 处理状态机逻辑
 */
static void Process_StateMachine(void)
{
    // 处理状态机
    StateMachine_Process(&g_state_machine);

    // 根据状态执行相应操作
    StateMachineState_t current_state = StateMachine_GetCurrentState(&g_state_machine);

    switch(current_state) {
        case STATE_MEASURING:
            // 执行超声波测量
            if(Ultrasonic_Measure() == ULTRASONIC_OK) {
                StateMachine_SendEvent(&g_state_machine, EVENT_SENSOR_DATA);
            }
            break;

        case STATE_ERROR:
            // 错误状态处理
            LED0 = 1; // 点亮错误指示LED
            break;

        default:
            break;
    }
}

