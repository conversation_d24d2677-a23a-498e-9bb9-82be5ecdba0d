#ifndef __ULTRASONIC_H
#define __ULTRASONIC_H

#include "sys.h"

// HC-SR04超声波传感器引脚定义
#define ULTRASONIC_TRIG_PORT    GPIOE
#define ULTRASONIC_TRIG_PIN     GPIO_PIN_2
#define ULTRASONIC_TRIG_CLK     __HAL_RCC_GPIOE_CLK_ENABLE

#define ULTRASONIC_ECHO_PORT    GPIOE
#define ULTRASONIC_ECHO_PIN     GPIO_PIN_3
#define ULTRASONIC_ECHO_CLK     __HAL_RCC_GPIOE_CLK_ENABLE

// 超声波传感器参数
#define ULTRASONIC_MAX_DISTANCE     400     // 最大测量距离(cm)
#define ULTRASONIC_MIN_DISTANCE     2       // 最小测量距离(cm)
#define ULTRASONIC_TIMEOUT          30000   // 超时时间(us)
#define ULTRASONIC_SOUND_SPEED      340     // 声速(m/s)

// 超声波传感器状态
typedef enum {
    ULTRASONIC_OK = 0,
    ULTRASONIC_ERROR,
    ULTRASONIC_TIMEOUT,
    ULTRASONIC_OUT_OF_RANGE
} UltrasonicStatus_t;

// 超声波传感器数据结构
typedef struct {
    float distance_cm;          // 距离(厘米)
    float distance_mm;          // 距离(毫米)
    u32 echo_time_us;          // 回波时间(微秒)
    UltrasonicStatus_t status; // 传感器状态
    u32 last_measure_time;     // 上次测量时间
    u8 is_measuring;           // 是否正在测量
} UltrasonicData_t;

// 函数声明
void Ultrasonic_Init(void);
UltrasonicStatus_t Ultrasonic_Measure(void);
float Ultrasonic_GetDistance_CM(void);
float Ultrasonic_GetDistance_MM(void);
UltrasonicStatus_t Ultrasonic_GetStatus(void);
void Ultrasonic_StartMeasure(void);
u8 Ultrasonic_IsMeasureComplete(void);
void Ultrasonic_ProcessMeasure(void);

// 内部函数声明
static void Ultrasonic_TriggerPulse(void);
static u32 Ultrasonic_MeasureEchoTime(void);
static float Ultrasonic_CalculateDistance(u32 echo_time_us);

// 全局变量声明
extern UltrasonicData_t g_ultrasonic_data;

#endif /* __ULTRASONIC_H */
