#ifndef __DISPLAY_MANAGER_H
#define __DISPLAY_MANAGER_H

#include "sys.h"
#include "lcd.h"
#include "ultrasonic.h"
#include "adc.h"
#include "state_machine.h"

// 显示区域定义
#define DISPLAY_TITLE_X         30
#define DISPLAY_TITLE_Y         30
#define DISPLAY_STATUS_X        30
#define DISPLAY_STATUS_Y        60
#define DISPLAY_ULTRASONIC_X    30
#define DISPLAY_ULTRASONIC_Y    90
#define DISPLAY_ADC_X           30
#define DISPLAY_ADC_Y           150
#define DISPLAY_TIME_X          30
#define DISPLAY_TIME_Y          220
#define DISPLAY_DEBUG_X         30
#define DISPLAY_DEBUG_Y         250

// 显示参数
#define DISPLAY_FONT_SIZE       16
#define DISPLAY_LARGE_FONT      24
#define DISPLAY_SMALL_FONT      12
#define DISPLAY_LINE_HEIGHT     20
#define DISPLAY_LARGE_LINE      30

// 显示模式
typedef enum {
    DISPLAY_MODE_MAIN = 0,      // 主显示模式
    DISPLAY_MODE_DEBUG,         // 调试模式
    DISPLAY_MODE_SETTINGS,      // 设置模式
    DISPLAY_MODE_GRAPH,         // 图形模式
    DISPLAY_MODE_MAX
} DisplayMode_t;

// 显示状态
typedef enum {
    DISPLAY_STATUS_OK = 0,
    DISPLAY_STATUS_ERROR,
    DISPLAY_STATUS_BUSY,
    DISPLAY_STATUS_UPDATING
} DisplayStatus_t;

// 显示数据结构
typedef struct {
    DisplayMode_t current_mode;     // 当前显示模式
    DisplayStatus_t status;         // 显示状态
    u32 last_update_time;          // 上次更新时间
    u32 update_interval;           // 更新间隔(ms)
    u8 auto_update;                // 自动更新使能
    u8 backlight_level;            // 背光亮度(0-100)
    u16 background_color;          // 背景颜色
    u16 text_color;                // 文字颜色
    u16 highlight_color;           // 高亮颜色
    u16 error_color;               // 错误颜色
} DisplayManager_t;

// 图形数据结构
typedef struct {
    float data_buffer[240];        // 数据缓冲区
    u16 buffer_index;              // 缓冲区索引
    float min_value;               // 最小值
    float max_value;               // 最大值
    u16 graph_x;                   // 图形X坐标
    u16 graph_y;                   // 图形Y坐标
    u16 graph_width;               // 图形宽度
    u16 graph_height;              // 图形高度
} GraphData_t;

// 函数声明
void DisplayManager_Init(void);
void DisplayManager_Update(void);
void DisplayManager_SetMode(DisplayMode_t mode);
DisplayMode_t DisplayManager_GetMode(void);
void DisplayManager_SetUpdateInterval(u32 interval_ms);
void DisplayManager_EnableAutoUpdate(u8 enable);

// 显示内容函数
void DisplayManager_ShowTitle(void);
void DisplayManager_ShowSystemStatus(void);
void DisplayManager_ShowUltrasonicData(void);
void DisplayManager_ShowADCData(void);
void DisplayManager_ShowTimeInfo(void);
void DisplayManager_ShowDebugInfo(void);

// 显示模式函数
void DisplayManager_ShowMainScreen(void);
void DisplayManager_ShowDebugScreen(void);
void DisplayManager_ShowSettingsScreen(void);
void DisplayManager_ShowGraphScreen(void);

// 图形显示函数
void DisplayManager_InitGraph(GraphData_t* graph, u16 x, u16 y, u16 width, u16 height);
void DisplayManager_AddGraphData(GraphData_t* graph, float value);
void DisplayManager_DrawGraph(GraphData_t* graph);
void DisplayManager_ClearGraph(GraphData_t* graph);

// 颜色和样式函数
void DisplayManager_SetColors(u16 bg_color, u16 text_color, u16 highlight_color);
void DisplayManager_SetBacklight(u8 level);
void DisplayManager_ClearScreen(void);
void DisplayManager_DrawFrame(u16 x, u16 y, u16 width, u16 height);

// 文本显示辅助函数
void DisplayManager_ShowText(u16 x, u16 y, const char* text, u16 color, u8 font_size);
void DisplayManager_ShowNumber(u16 x, u16 y, float number, u8 decimals, u16 color, u8 font_size);
void DisplayManager_ShowStatus(u16 x, u16 y, const char* status, u8 is_error);

// 进度条和指示器
void DisplayManager_DrawProgressBar(u16 x, u16 y, u16 width, u16 height, u8 progress);
void DisplayManager_DrawLevelIndicator(u16 x, u16 y, float value, float min_val, float max_val);

// 全局变量声明
extern DisplayManager_t g_display_manager;
extern GraphData_t g_ultrasonic_graph;
extern GraphData_t g_adc_graph[ADC_CHANNEL_COUNT];

#endif /* __DISPLAY_MANAGER_H */
